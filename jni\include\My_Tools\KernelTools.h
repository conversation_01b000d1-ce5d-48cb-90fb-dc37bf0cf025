//
// Created by s2049 on 24-7-14.
//

#ifndef KERNELTOOLS_H
#define KERNELTOOLS_H

#include <iostream>
#include <cstdio>
#include <cstdlib>
#include <cstring>
#include <unistd.h>
#include <fcntl.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <sys/stat.h>
#include <pthread.h>
#include <dirent.h>
#include <sys/types.h>
#include <cstdint>
#include <cstddef>
#include <sys/types.h>
#include <unistd.h>
#include <cstdint>
#include <cstddef>
#include <sys/types.h>
#include <unistd.h>
#include <stdio.h>
#include <dlfcn.h>
#include <jni.h>

namespace driver {
    // KMA驱动核心函数
    bool read(uintptr_t addr, void *buffer, size_t size);
    bool read_safe(uintptr_t addr, void *buffer, size_t size);
    bool write(uintptr_t addr, void *buffer, size_t size);

    // 模块和进程管理
    uintptr_t get_module_base(char *name, pid_t pid);
    uintptr_t get_module_base(pid_t pid, char *name, size_t size);
    pid_t get_pid(char *name);
    pid_t get_pid(char *name, char *comm);

    // 系统管理
    void cpuset(int num);
    void cpuset(int start, int end);
    void cleanup();
    bool initialkernel(pid_t gamepid);

    // 模板函数
    template <typename T>
    T read(uintptr_t addr) {
        T res;
        if (read(addr, &res, sizeof(T)))
            return res;
        return {};
    }

    template <typename T>
    T read_safe(uintptr_t addr) {
        T res;
        if (read_safe(addr, &res, sizeof(T)))
            return res;
        return {};
    }

    template <typename T>
    bool write(uintptr_t addr, T value) {
        return write(addr, &value, sizeof(T));
    }
}

#endif //KERNELTOOLS_H
