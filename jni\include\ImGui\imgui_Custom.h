#ifndef IMGUI_CUSTOMIZE_H
#define IMGUI_CUSTOMIZE_H

#include <chrono>
#include <string>
#include <vector>
#include "imgui.h"
#include "imgui_internal.h"

namespace fidenBoard {
    enum flags {
        flags_default = 0,
        flags_noNumbers = 1,
        flags_noCaps = 2
    };

    class Size {
    public:
        int width, height;
        Size(int width = 800, int height = 800);
    };

    class InputText {
    private:
        std::string text;
    public:
        void setText(std::string val);
        std::string getText();
        std::string* getAddress();
    };

    class Properties {
    private:
        flags flag;
        Size size;
        void initSize(ImVec2 sizze);
    public:
        Properties(ImVec2 size, flags flag = flags_default);
        flags getFlags();
        void setFlags(flags flag);
    };

    class Lines {
    public:
        std::string lines[3];
        Lines();
        void toCaps(bool caps);
        int getSize();
    };

    namespace specialChars {
        class Canc {
        public:
            void add(float width=80, float height=80);
        };

        extern bool isCaps;

        class Caps {
        public:
            void add(float width=80, float height=80);
        };

        class Space {
        public:
            void add();
        };
    }

    class FidenBoard {
    private:
        Properties *properties;
        specialChars::Canc canc;
        specialChars::Space space;
        specialChars::Caps caps;
        void addKeyword(std::string carattere, float width, float height);
        void addKeywordSameLine(std::string str, int width=80, int height=80);
    public:
        FidenBoard(ImVec2 size, flags flag = flags_default);
        std::string getInputText();
        void setInputText(std::string val);
    };
}

struct DynamicIslandMessage {
    std::string content;
    float duration;
    std::chrono::steady_clock::time_point startTime;
    bool persistent;
    float fadeOutProgress;  // To track fade out animation
    float bounceOffset;

    DynamicIslandMessage(const std::string& msg, float dur)
        : content(msg), duration(dur), persistent(dur < 0.0f), fadeOutProgress(1.0f) {
        startTime = std::chrono::steady_clock::now();
    }

    bool isExpired() const {
        if (persistent) return false;
        auto now = std::chrono::steady_clock::now();
        return std::chrono::duration<float>(now - startTime).count() > duration;
    }

    float timeRemaining() const {
        auto now = std::chrono::steady_clock::now();
        return duration - std::chrono::duration<float>(now - startTime).count();
    }
};

class DynamicIsland {
public:
    DynamicIsland(){}

    void addMessage(const std::string& content, float duration);

    void render(ImDrawList* drawList);

private:
    std::vector<DynamicIslandMessage> messages;
    std::vector<float> animations;  // Store scale animations for each message

    void cleanExpiredMessages() {
        messages.erase(
            std::remove_if(messages.begin(), messages.end(), [](DynamicIslandMessage& msg) {
                return msg.isExpired() && msg.fadeOutProgress <= 0.0f;
            }),
            messages.end());
    }

    void applyAnimations() {
        auto now = std::chrono::steady_clock::now();
        if (animations.size() < messages.size()) {
            animations.resize(messages.size(), 0.0f);
        }

        for (size_t i = 0; i < messages.size(); ++i) {
            auto timeSinceAdded = std::chrono::duration<float>(now - messages[i].startTime).count();

            // 进入动画：弹性缩放从0到1
            if (timeSinceAdded < 0.5f) {
                animations[i] = easeOutElastic(timeSinceAdded * 2.0f);
            } else {
                animations[i] = 1.0f;
            }

            // 消失动画：压缩和向上回弹
            if (messages[i].isExpired()) {
                float fadeOutDuration = 1.0f;  // 消失动画时长1秒
                float timeSinceExpired = std::chrono::duration<float>(now - (messages[i].startTime + std::chrono::seconds((int)messages[i].duration))).count();
                float progress = std::min(timeSinceExpired / fadeOutDuration, 1.0f);  // 从0到1的进度

                // 消失动画：压缩 + 渐隐 + 向上移动
                animations[i] = 1.0f - progress;  // 尺寸逐渐缩小
                messages[i].fadeOutProgress = 1.0f - progress;  // 渐隐效果

                // 实现向上回弹的效果
                float bounceOffset = easeOutElastic(progress) * 20.0f;  // 回弹距离为20像素
                messages[i].bounceOffset = -bounceOffset;  // 向上移动
            }
        }
    }

    // 改进的easeOutElastic函数仍然可以用于弹性缩放
    float easeOutElastic(float t) {
        return t < 0.5 ? 2 * t * t : 1 - pow(-2 * t + 2, 2);
    }

    ImU32 adjustAlpha(ImU32 color, float alpha) {
        ImVec4 col = ImGui::ColorConvertU32ToFloat4(color);
        col.w *= alpha;
        return ImGui::ColorConvertFloat4ToU32(col);
    }
};


struct ButtonAnimation {
    float currentY;
    float targetY;
    bool isAnimating;
};

namespace NotificationSystem {

    class NotificationManager {
    public:
        NotificationManager();

        void AddNotification(const std::string& message, float displayTime, ImVec2 Pos);

        void UpdateAndRender();

    private:
        struct Impl;
        Impl* pImpl;
    };
}

namespace ImGui {
    // 自定义::(控件)小组件
	IMGUI_API bool          M_SwitchButton(const char* str_id, bool* v, const ImVec2& size = ImVec2(2.30f, 0.82f));
    IMGUI_API bool          ButtonTextColored(const ImVec4& col, const char* fmt, ...);
    IMGUI_API bool          M_shut(const char* text);
    IMGUI_API void          M_shut(const char* text, bool* close);
    IMGUI_API void          M_shut_G(const char* text, bool* close, const ImVec4& col);
    IMGUI_API void          CenteredVerticalRadioGroup(const char* label, int* selectedIndex, const char* options[], int numOptions, float width, float totalHeight, float buttonHeight, float smoothness);
    IMGUI_API void          RenderVerticalMenu(const std::vector<std::string>& menu_items, int& selected_index);

}

#endif //IMGUI_CUSTOMIZE_H
