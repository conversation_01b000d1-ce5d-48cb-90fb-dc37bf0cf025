//
// Created by s2049 on 24-7-14.
//

#include "Hack.h"

#include <fstream>
#include <sstream>

#include "ConfigManager.h"
#include "GameTools.h"
#include "KernelTools.h"
#include "LinuxSystem_Tools.h"
#include "draw.h"

using namespace std;

pid_t pid;
UpData MainUpData;
vector<IntoList> MainIntoList;
vector<PlayerList> MainPlayerList;
uintptr_t libUe4_address, libTemp_address;

// 添加物资分类映射表初始化
unordered_map<int, unordered_map<string, string>> GameTools::ItemsClassNameMap = {
    {1, {// 投掷物类
         {"ojGrenade_BP_C", "手雷"},
         {"BP_AirraidActor_C", "空袭"},
         {"BP_TraceGrenade", "追踪雷"},
         {"BP_WEP_DragonBoySpear_C", "雷枪"},
         {"BuildingActor_ConcertStage_MusicGirl_BP_C", "狗窝"},
         {"ojBurn_BP_C", "燃烧弹"}}},
    {2, {// 载具类
         {"_Mountainbike_Training_C", "自行车"},
         {"Mirado", "双人跑车"},
         {"Scooter", "小绵羊"},
         {"VH_Horse", "马"},
         {"_BRDM_C", "装甲车"},
         {"VH_Motorcycle_C", "摩托车"},
         {"Snowmobile", "雪地摩托"},
         {"StationWagon", "旅行车"},
         {"BP_VH_Buggy", "蹦蹦车"},
         {"VH_Dacia_", "轿车"},
         {"VH_UAZ01_New_C", "吉普车"},
         {"PickUp_07_C", "皮卡车"},
         {"CoupeRB", "双人跑车"},
         {"_MiniBus_01_C", "迷你巴士"},
         {"_PG117_C", "快艇"},
         {"uaRail_1_C", "摩托艇"},
         {"_Motorglider_C", "滑翔机"},
         {"BP_VH_Bigfoot_C", "大脚车"},
         {"VH_ATV1_C", "四轮摩托"},
         {"Rony_01_C", "行李车"},
         {"VH_UTV_C", "越野车"},
         {"BP_VH_Tuk_1_C", "三轮车"},
         {"VH_Snowmobile_C", "雪橇车"},
         {"PG117", "船"},
         {"VH_4SportCar_C", "跑车"},
         {"BP_Excavator_C", "挖掘机"},
         {"VH_Kite_C", "风筝"},
         {"VH_Drift", "拉力赛车"},
         {"VH_Blanc_C", "SUV电车"},
         {"VH_Picobus_C", "大巴车"},
         {"VH_DumpTruck_C", "泥土车"},
         {"VH_Excavator_C", "挖掘机"},
         {"HugeMouthJet_RPG_C", "战斗机"},
         {"VH_LostMobile_C", "霹雳车"},
         {"VH_DesertCar_C", "沙漠越野车"}}},
    {3, {// 防具类
         {"BP_Armor_Lv3_C", "三级甲"},
         {"Bag_Lv3", "三级包"},
         {"BP_Helmet_Lv3_C", "三级头"}}},
    {4, {// 道具类
         {"BP_Pistol_RevivalFlaregun", "召回信号枪"},
         {"BP_Ammo_RevivalFlare_Pickup_C", "召回信号弹"},
         {"BP_Ammo_Flare_Pickup_C", "信号弹"},
         {"MilitarySupplyBoxBase_Baltic", "主题箱子"},
         {"MilitarySupp", "主题箱子"},
         {"CG027_Lottery_C", "扭蛋机"},
         {"Pistol_Flaregun", "信号枪"},
         {"revivalAED_Pickup_C", "自救器"},
         {"BP_Neon_Coin_Pickup_C", "钱币"},
         {"BP_Pickup_Finger_C", "钩锁"},
         {"AirDropBox_C", "空投"},
         {"BP_Grenade_EmergencyCall_Weapon_Wrapper_C", "紧急呼救器"},
         {"BP_Other_Mortar_Bullet_C", "注意迫击炮"},
         {"BP_AirDropBox_SuperPeople_C", "空投"},
         {"AirDropListWrapperActor", "空投"},
         {"BP_AirdropChipBox_C", "金仓"},
         {"CG030_Market_SafeBox_C", "保险"},
         {"CG030_Market_SafeBox_2_C", "保险"},
         {"perPeopleSkill", "金插"}}},
    {5, {// 盒子
         {"_PlayerDeadListWrapper_C", "盒子"}}},
    {6, {// 药品
         {"ink_Pickup_C", "饮料"},
         {"lls_Pickup_C", "止痛药"},
         {"jection_Pickup_C", "肾上腺素"},
         {"rstAidbox_Pickup_C", "医疗箱"}}},
    {7, {// 子弹
         {"Ammo_556", "556子弹"},
         {"Ammo_9mm", "9mm子弹"},
         {"Ammo_45AC", "45子弹"},
         {"Ammo_762", "762子弹"}}},
    {8, {// 762枪械
         {"BP_Rifle_AKM_Wrapper_C", "AKM"},
         {"BP_Rifle_ACE32_Wrapper_C", "ACE32"},
         {"BP_Rifle_HoneyBadger_Wrapper_C", "蜜獾"},
         {"BP_Rifle_M762_Wrapper_C", "M762"},
         {"BP_Rifle_Groza_Wrapper_C", "Groza"},
         {"BP_Other_PKM_Wrapper_C", "PKM"}}},
    {9, {// 556枪械
         {"BP_Rifle_SCAR_Wrapper_C", "SCAR"},
         {"BP_Rifle_M416_Wrapper_C", "M416"},
         {"BP_Rifle_G36_Wrapper_C", "G36"},
         {"BP_Rifle_Famas_Wrapper_C", "Famas"},
         {"BP_Other_MG36_Wrapper_C", "MG36"}}},

    {10, {// 冲锋枪
          {"BP_MachineGun_UMP9_Wrapper_C", "UMP45"},
          {"BP_MachineGun_AKS74U_Wrapper_C", "AKS74U"},
          {"BP_MachineGun_Vector_Wrapper_C", "Vector"},
          {"QK_Mid_Suppressor", "冲消"},
          {"DJ_Mid_EQ", "冲锋快扩"}}},

    {11, {// 霰弹枪
          {"BP_ShotGun_S12K_Wrapper_C", "S12K"},
          {"BP_ShotGun_DP12_Wrapper_C", "DBS"},
          {"BP_ShotGun_SPAS_Wrapper_C", "SPAS"},
          {"BP_ShotGun_AA12_Wrapper_C", "AA12"},
          {"Ammo_12Guage", "喷子子弹"},
          {"BP_QK_Choke_Pickup", "收束器"},
          {"BP_QK_DuckBill_Pickup", "鸭嘴"},
          {"BP_DJ_ShotGun_Pickup_C", "散弹快速"}}},

    {12, {// 狙击枪
          {"BP_Sniper_SVD_Wrapper_C", "SVD"},
          {"BP_Sniper_SKS_Wrapper_C", "SKS"},
          {"BP_Rifle_M417_Wrapper_C", "M417"},
          {"BP_Sniper_Mini14_Wrapper_C", "Mini14"},
          {"DJ_Sniper_EQ", "狙击快扩"},
          {"BP_QT_Sniper_Pickup", "狙击枪托"},
          {"QK_Sniper_Suppressor", "狙消"}}},

    {13, {// 其他
          {"BP_Other_Mortar_Wrapper_C", "迫击炮"},
          {"Ammo_Bolt", "箭"},
          {"BP_Other_HuntingBow_Wrapper_C", "爆炸弓"}}},

    {14, {// 步枪配件
          {"DJ_Large_EQ_Pickup_C", "步枪快扩"},
          {"BP_QT_A_Pickup", "步枪枪托"},
          {"BP_WB_LightGrip_Pickup_C", "轻型"},
          {"BP_WB_Lasersight_Pickup_C", "激光"},
          {"BP_WB_Angled_Pickup_C", "直角"},
          {"QK_Large_Suppressor", "步消"}}},

    {15, {// 倍镜
          {"MZJ_8X_Pickup_C", "8倍"},
          {"MZJ_6X_Pickup_C", "6倍"}}},

    {16, {// 地铁宝箱
          {"EscapeBox_SupplyBox_", "物资箱"},
          {"EscapeBoxHight_SupplyBox_", "物资箱"}}}};

vector<VehicleInfo> GameTools::MainVehicleList;

bool GetGame_TempBOOl = false;
int Head_Number, Chest_Number, Pelvis_Number, Left_Shoulder_Number,
    Right_Shoulder_Number, Left_Elbow_Number, Right_Elbow_Number,
    Left_Wrist_Number, Right_Wrist_Number, Left_Thigh_Number,
    Right_Thigh_Number, Left_Knee_Number, Right_Knee_Number, Left_Ankle_Number,
    Right_Ankle_Number;
int getProcessID(const char *packageName) {
  int id = -1;
  DIR *dir;
  FILE *fp;
  char filename[64];
  char cmdline[64];
  struct dirent *entry;
  dir = opendir("/proc");
  while ((entry = readdir(dir)) != NULL) {
    id = atoi(entry->d_name);
    if (id != 0) {
      sprintf(filename, "/proc/%d/cmdline", id);
      fp = fopen(filename, "r");
      if (fp) {
        fgets(cmdline, sizeof(cmdline), fp);
        fclose(fp);
        if (strcmp(packageName, cmdline) == 0) {
          return id;
        }
      }
    }
  }
  closedir(dir);
  return -1;
}

bool Init_StratHack() {
  pid = -1;
  MainPlayerList.reserve(100);
  MainUpData.ObjectActors.reserve(100);
  char key[] = "xifhehjd";
  char UE4Name[] = "libUE4.so";
  string GameThreadName = "com.tencent.tmgp.pubgmhd";
  pid = getProcessID(GameThreadName.c_str());
  if (pid <= 0)
    return false;
  if (!GetGame_TempBOOl) {
    if (!driver::initialkernel(pid)) {
      notificationManager->addMessage("您木有刷入任何读写驱动！", 2);
      return false;
    }
    // KMA驱动已自动初始化，无需额外设置
  }
  libUe4_address = driver::get_module_base(UE4Name, pid);
  GetGame_TempBOOl = true;
  if (!libUe4_address)
    return false;
  hack_mainThread();
  return true;
}

void hack_mainThread() {
  GameTools::smokeObjects.clear();
  GameTools::MainVehicleList.clear(); // 清空载具列表
  MainUpData.UWorld_address = driver::read<uintptr_t>(libUe4_address + GameOffset::UWorld_Offset);
  MainUpData.GNames_address = driver::read<uintptr_t>(libUe4_address + GameOffset::GNames_Offset);
  MainUpData.GMatrix_Offset = driver::read<uintptr_t>(driver::read<uintptr_t>(libUe4_address + GameOffset::GMatrix_Offset) + 0x20) + 0x270;
  GameTools::Gname_address = MainUpData.GNames_address;
  MainUpData.SelfController_address = driver::read<uintptr_t>(driver::read<uintptr_t>(driver::read<uintptr_t>(MainUpData.UWorld_address + GameOffset::UWorldToNectDriver_Offset) + GameOffset::NetDriverToServerConnection_Offset) + GameOffset::ServerConnectionToPlayerController_Offset);
  MainUpData.SelfActors_address = driver::read<uintptr_t>(MainUpData.SelfController_address + GameOffset::PlayerControllerToMyselfActor_Offset);
  if (MainUpData.SelfActors_address == 0) {
    return;
  }
  MainUpData.SelfSTExtraController_address = driver::read<uintptr_t>(MainUpData.SelfActors_address + GameOffset::STExtraPlayerController_Offset);    // 自身结构地址
  MainUpData.SelfCameraManager_address = driver::read<uintptr_t>(MainUpData.SelfSTExtraController_address + GameOffset::PlayerCameraManager_Offset); // 玩家相机
  uintptr_t Uleve = driver::read<uintptr_t>(MainUpData.UWorld_address + GameOffset::ULeve_Offset);
  int ObjectActirsSize = 0;
  MainUpData.ObjectActors_address = driver::read<uintptr_t>(Uleve + GameOffset::ULeveToActors_Offset);
  ObjectActirsSize = driver::read<int>(Uleve + GameOffset::ULeveToActorsCount_Offset);
  if (ObjectActirsSize > 0 && ObjectActirsSize < 2000) {
    vector<UpPlayer> tempUpPLayers;
    vector<IntoList> tempIntoList;
    MainUpData.Team = driver::read<int>(MainUpData.SelfActors_address + GameOffset::PlayerTeam_Offset);
    for (int i = 0; i < ObjectActirsSize; i++) {
      uintptr_t object_address = driver::read<uintptr_t>(MainUpData.ObjectActors_address + 8 * i);
      uintptr_t objectcoordptr_address = driver::read<uintptr_t>(object_address + GameOffset::RootComponent_Offset);
      MainUpData.bulletSpeed = driver::read<float>(driver::read<uintptr_t>(driver::read<uintptr_t>(MainUpData.SelfActors_address + 0x33f0 + 0x20) + 0x19d0) + 0x13f4);
      Vec3 objPos = driver::read<Vec3>(objectcoordptr_address + GameOffset::PlayertWorldPos_Offset);
      if (objPos.x == 0 || objPos.y == 0 || objPos.z == 0)
        continue;
      int oid = driver::read<int>(object_address + 0x18);
      string ClassName = GameTools::GetFromFName(oid);
      if (driver::read<float>(object_address + GameOffset::PlayerHight_Offset) == 479.5) {
        int State = driver::read<int>(object_address + GameOffset::State_Offset);
        if (State == 262144 || State == 262152) {
          continue;
        }
        int Team = driver::read<int>(object_address + GameOffset::PlayerTeam_Offset);
        if (Team == MainUpData.Team)
          continue;
        UpPlayer temp_up_player;
        temp_up_player.address = object_address;
        temp_up_player.coordptr_address = objectcoordptr_address;
        temp_up_player.objPos = objPos;
        temp_up_player.State = State;
        temp_up_player.Team = Team;
        temp_up_player.IsRobot = driver::read<int>(object_address + GameOffset::IsBot_Offset);
        if (!ConfigManager::Settings.isShowBot && temp_up_player.IsRobot)
          continue;
        if (temp_up_player.IsRobot)
          temp_up_player.PlayerName = "RoBot" + to_string(i);
        else
          temp_up_player.PlayerName = GameTools::getPlayerName(driver::read<uintptr_t>(object_address + GameOffset::PlayerName_Offset));
        tempUpPLayers.push_back(temp_up_player);
      } else {
        if (ClassName == "ProjSmoke_BP_C") {
          smokeObject newSmokeObject;
          newSmokeObject.worldPos = objPos;
          GameTools::smokeObjects.push_back(newSmokeObject);
        }
        if (ConfigManager::Settings.isShowarticle) {
          if (ConfigManager::Settings.DEBUG) {
            IntoList temp_into_list;
            temp_into_list.address = object_address;
            temp_into_list.Pos = objPos;
            temp_into_list.Type = -1;
            temp_into_list.Name = ClassName;
            temp_into_list.color = ImColor(255, 255, 255, 255);
            tempIntoList.push_back(temp_into_list);
          } else {
            // 检查ItemsClassNameMap中的物品
            for (const auto &categoryPair : GameTools::ItemsClassNameMap) {
              int categoryType = categoryPair.first;
              const auto &itemMap = categoryPair.second;
              // 检查该类型是否启用显示
              if (ConfigManager::Settings.ShowItemTypes.find(categoryType) != ConfigManager::Settings.ShowItemTypes.end() &&
                  ConfigManager::Settings.ShowItemTypes[categoryType]) {
                // 使用模糊匹配检查类名是否包含在该类型中
                for (const auto &entry : itemMap) {
                  if (ClassName.find(entry.first) != std::string::npos) {
                    IntoList temp_into_list;
                    temp_into_list.address = object_address;
                    temp_into_list.Pos = objPos;
                    temp_into_list.Type = categoryType;
                    temp_into_list.Name = entry.second;

                    // 根据物品类型设置不同颜色
                    switch (categoryType) {
                    case 1:                                           // 投掷物类
                      temp_into_list.color = ImColor(235, 0, 0, 255); // 红色
                      break;
                    case 2:                                           // 载具类
                      temp_into_list.color = ImColor(0, 255, 0, 255); // 绿色
                      break;
                    case 3:                                           // 防具类
                      temp_into_list.color = ImColor(0, 0, 255, 255); // 蓝色
                      break;
                    case 4:                                             // 道具类
                      temp_into_list.color = ImColor(255, 255, 0, 255); // 黄色
                      break;
                    case 5:                                             // 盒子
                      temp_into_list.color = ImColor(255, 0, 255, 255); // 紫色
                      break;
                    case 6:                                             // 药品
                      temp_into_list.color = ImColor(0, 255, 255, 255); // 青色
                      break;
                    case 7:                                             // 子弹
                      temp_into_list.color = ImColor(255, 165, 0, 255); // 橙色
                      break;
                    case 8:                                              // 762枪械
                      temp_into_list.color = ImColor(255, 20, 147, 255); // 深粉色
                      break;
                    case 9:                                             // 556枪械
                      temp_into_list.color = ImColor(50, 205, 50, 255); // 浅绿色
                      break;
                    case 10:                                           // 冲锋枪
                      temp_into_list.color = ImColor(255, 69, 0, 255); // 红橙色
                      break;
                    case 11:                                             // 霰弹枪
                      temp_into_list.color = ImColor(138, 43, 226, 255); // 蓝紫色
                      break;
                    case 12:                                            // 狙击枪
                      temp_into_list.color = ImColor(220, 20, 60, 255); // 深红色
                      break;
                    case 13:                                              // 其他
                      temp_into_list.color = ImColor(128, 128, 128, 255); // 灰色
                      break;
                    case 14:                                            // 步枪配件
                      temp_into_list.color = ImColor(255, 215, 0, 255); // 金色
                      break;
                    case 15:                                            // 倍镜
                      temp_into_list.color = ImColor(0, 191, 255, 255); // 深天蓝色
                      break;
                    case 16:                                            // 地铁宝箱
                      temp_into_list.color = ImColor(255, 140, 0, 255); // 深橙色
                      break;
                    default:
                      temp_into_list.color = ImColor(255, 255, 255, 255); // 白色
                      break;
                    }

                    tempIntoList.push_back(temp_into_list);
                    goto item_found; // 找到匹配项后跳出所有循环
                  }
                }
              }
            }
            item_found:;
          }
        }
      }
    }
    MainIntoList.swap(tempIntoList);
    MainUpData.ObjectActors.swap(tempUpPLayers);
  }
}

uintptr_t CameraPOV_address;
double StartReadTime = 0;
double NowReadTime = 0;
void UpPlayerData() {
    NowReadTime = ImGui::GetTime();
    if (StartReadTime == 0)
      StartReadTime = NowReadTime;
    if (NowReadTime - StartReadTime > 2) {
      StartReadTime = NowReadTime;
      MainIntoList.clear();
      hack_mainThread();
    }
    MainPlayerList.clear();
    if (MainUpData.UWorld_address == 0)
      return;
    if (MainUpData.ObjectActors.empty() && MainIntoList.empty())
      return;
    MainUpData.SelfCoordinate = driver::read<Vec3>(driver::read<uintptr_t>(MainUpData.SelfActors_address + GameOffset::RootComponent_Offset) + GameOffset::PlayertWorldPos_Offset);
    if (auto CurrentUsingWeaponSafety = driver::read<uintptr_t>(MainUpData.SelfActors_address + GameOffset::CurrentUsingWeaponSafety_Offset)) {
      MainUpData.SelfWeaponType = driver::read<int>(CurrentUsingWeaponSafety + GameOffset::ShootWeaponType_Offset);
    }
    CameraPOV_address = MainUpData.SelfCameraManager_address + GameOffset::CameraCache_Offset + GameOffset::CameraPOV_Offset;
    MainUpData.SelfViewInfo.Location = driver::read<Vec3>(CameraPOV_address + GameOffset::CameraLocation_Offset);
    MainUpData.SelfViewInfo.LocationLocalSpace = driver::read<Vec3>(CameraPOV_address + GameOffset::CameraLocationLocalSpace_Offset);
    MainUpData.SelfViewInfo.Rotation = driver::read<Rotator>(CameraPOV_address + GameOffset::CameraRotation_Offset);
    MainUpData.SelfViewInfo.FOV = driver::read<float>(CameraPOV_address + GameOffset::CameraFOV_Offset);
    GameTools::worldMatrix = GameTools::rotatorToMatrix(MainUpData.SelfViewInfo.Rotation);
    MainUpData.SelfState = driver::read<int>(driver::read<uintptr_t>(MainUpData.SelfActors_address + GameOffset::State_Offset));
    MainUpData.SelfControlRotation = driver::read<Rotator>(MainUpData.SelfController_address + GameOffset::ControlRotation_Offset);
    MainUpData.IsFiring = driver::read<bool>(MainUpData.SelfActors_address + GameOffset::IsFiring_Offset);
    MainUpData.IsAiming = MainUpData.SelfViewInfo.FOV <= 75;

    for (auto &smoke : GameTools::smokeObjects) {
      GameTools::GetDistance(smoke.worldPos, MainUpData.SelfCoordinate, &smoke.screenDistance);
      // 底部
      smoke.screenPos[0] = GameTools::WorldToScreen({smoke.worldPos.x - 400, smoke.worldPos.y + 400, smoke.worldPos.z});
      smoke.screenPos[1] = GameTools::WorldToScreen({smoke.worldPos.x + 400, smoke.worldPos.y + 400, smoke.worldPos.z});
      smoke.screenPos[2] = GameTools::WorldToScreen({smoke.worldPos.x - 400, smoke.worldPos.y - 400, smoke.worldPos.z});
      smoke.screenPos[3] = GameTools::WorldToScreen({smoke.worldPos.x + 400, smoke.worldPos.y - 400, smoke.worldPos.z});
      // 顶部
      smoke.screenPos[4] = GameTools::WorldToScreen({smoke.worldPos.x - 400, smoke.worldPos.y + 400, smoke.worldPos.z + 300});
      smoke.screenPos[5] = GameTools::WorldToScreen({smoke.worldPos.x + 400, smoke.worldPos.y + 400, smoke.worldPos.z + 300});
      smoke.screenPos[6] = GameTools::WorldToScreen({smoke.worldPos.x - 400, smoke.worldPos.y - 400, smoke.worldPos.z + 300});
      smoke.screenPos[7] = GameTools::WorldToScreen({smoke.worldPos.x + 400, smoke.worldPos.y - 400, smoke.worldPos.z + 300});
    }

    for (int i = 0; i < MainUpData.ObjectActors.size(); i++) {
      PlayerList TempPlayerList;
      const UpPlayer &TempUpPlayer = MainUpData.ObjectActors[i];
      TempPlayerList.Pos = TempUpPlayer.objPos;
      TempPlayerList.State = TempUpPlayer.State;
      TempPlayerList.IsBot = TempUpPlayer.IsRobot;
      TempPlayerList.TeamID = TempUpPlayer.Team;
      TempPlayerList.PlayerName = TempUpPlayer.PlayerName;

      float MinHealth = driver::read<float>(TempUpPlayer.address + GameOffset::Health_Offset);
      float MaxHealth = driver::read<float>(TempUpPlayer.address + GameOffset::Health_Offset + 0x8);
      TempPlayerList.Health = (MinHealth / MaxHealth) * 100;
      TempPlayerList.VelocitySafety = driver::read<Vec3>(TempUpPlayer.address + 0xfdc);

      if (driver::read<int>(TempUpPlayer.address + GameOffset::bDead_Offset) == 1)
        continue;
      GameTools::GetDistance(TempPlayerList.Pos, MainUpData.SelfCoordinate, &TempPlayerList.Distance);
      Vec2 lowPlayerScreenPos;
      Vec3 lowPlayerPos = {TempPlayerList.Pos.x, TempPlayerList.Pos.y, TempPlayerList.Pos.z - 165};
      GameTools::WorldToScreen(&TempPlayerList.ScreenPos, TempPlayerList.Pos);
      GameTools::WorldToScreen(&lowPlayerScreenPos, lowPlayerPos);
      TempPlayerList.width = lowPlayerScreenPos.y - TempPlayerList.ScreenPos.y;
      TempPlayerList.WeaponID = driver::read<int>(driver::read<uintptr_t>(driver::read<uintptr_t>(TempUpPlayer.address + GameOffset::WeaponManagerComponent_Offset) + GameOffset::CurrentWeaponReplicated_Offset) + GameOffset::ShootWeaponType_Offset);
      uintptr_t Mesh = driver::read<uintptr_t>(TempUpPlayer.address + GameOffset::BoneMesh_Offset);
      uintptr_t Human = Mesh + GameOffset::BoneHuman_Offset;
      uintptr_t Bone = driver::read<uintptr_t>(Mesh + GameOffset::BonePtr_Offset) + 0x30;
      TempPlayerList.BoneCount = driver::read<int>(Mesh + GameOffset::BonePtr_Offset + 0x8);
      GameTools::GetBoneInit(Human, Bone);

      if (TempPlayerList.BoneCount == 70) {
        Head_Number = 5;
        Chest_Number = 4;
        Pelvis_Number = 0;
        Left_Shoulder_Number = 13;
        Right_Shoulder_Number = 35;
        Left_Elbow_Number = 14;
        Right_Elbow_Number = 36;
        Left_Wrist_Number = 30;
        Right_Wrist_Number = 52;
        Left_Thigh_Number = 57;
        Right_Thigh_Number = 61;
        Left_Knee_Number = 58;
        Right_Knee_Number = 62;
        Left_Ankle_Number = 59;
        Right_Ankle_Number = 63;
      } else if (TempPlayerList.BoneCount == 61) {
        Head_Number = 5;
        Chest_Number = 27;
        Pelvis_Number = 0;
        Left_Shoulder_Number = 7;
        Right_Shoulder_Number = 28;
        Left_Elbow_Number = 8;
        Right_Elbow_Number = 29;
        Left_Wrist_Number = 59;
        Right_Wrist_Number = 58;
        Left_Thigh_Number = 48;
        Right_Thigh_Number = 52;
        Left_Knee_Number = 49;
        Right_Knee_Number = 53;
        Left_Ankle_Number = 50;
        Right_Ankle_Number = 54;
      } else if (TempPlayerList.BoneCount == 63) {
        Head_Number = 5;
        Chest_Number = 29;
        Pelvis_Number = 0;
        Left_Shoulder_Number = 9;
        Right_Shoulder_Number = 30;
        Left_Elbow_Number = 10;
        Right_Elbow_Number = 31;
        Left_Wrist_Number = 24;
        Right_Wrist_Number = 60;
        Left_Thigh_Number = 50;
        Right_Thigh_Number = 54;
        Left_Knee_Number = 51;
        Right_Knee_Number = 55;
        Left_Ankle_Number = 52;
        Right_Ankle_Number = 56;
      } else if (TempPlayerList.BoneCount == 68) {
        Head_Number = 5;
        Chest_Number = 4;
        Pelvis_Number = 0;
        Left_Shoulder_Number = 11;
        Right_Shoulder_Number = 33;
        Left_Elbow_Number = 12;
        Right_Elbow_Number = 34;
        Left_Wrist_Number = 15;
        Right_Wrist_Number = 65;
        Left_Thigh_Number = 55;
        Right_Thigh_Number = 59;
        Left_Knee_Number = 56;
        Right_Knee_Number = 60;
        Left_Ankle_Number = 57;
        Right_Ankle_Number = 61;
      } else if (TempPlayerList.BoneCount == 65) {
        Head_Number = 5;
        Chest_Number = 4;
        Pelvis_Number = 0;
        Left_Shoulder_Number = 11;
        Right_Shoulder_Number = 32;
        Left_Elbow_Number = 12;
        Right_Elbow_Number = 33;
        Left_Wrist_Number = 63;
        Right_Wrist_Number = 62;
        Left_Thigh_Number = 52;
        Right_Thigh_Number = 56;
        Left_Knee_Number = 53;
        Right_Knee_Number = 57;
        Left_Ankle_Number = 54;
        Right_Ankle_Number = 58;
      } else {
        Head_Number = 5;
        Chest_Number = 4;
        Pelvis_Number = 0;
        Left_Shoulder_Number = 11;
        Right_Shoulder_Number = 33;
        Left_Elbow_Number = 12;
        Right_Elbow_Number = 34;
        Left_Wrist_Number = 15;
        Right_Wrist_Number = 65;
        Left_Thigh_Number = 55;
        Right_Thigh_Number = 59;
        Left_Knee_Number = 56;
        Right_Knee_Number = 60;
        Left_Ankle_Number = 57;
        Right_Ankle_Number = 61;
      }

      /* 头部 */
      GameTools::GetBoneData(Head_Number * 48, &TempPlayerList.player_bone.Head, 7);
      /* 胸部 */ !GameTools::GetBoneData(Chest_Number * 48, &TempPlayerList.player_bone.Chest);
      /* 盆骨 */
      GameTools::GetBoneData(Pelvis_Number * 48, &TempPlayerList.player_bone.Pelvis);
      /* 左肩膀 */
      GameTools::GetBoneData(Left_Shoulder_Number * 48, &TempPlayerList.player_bone.Left_Shoulder);
      /* 右肩膀 */
      GameTools::GetBoneData(Right_Shoulder_Number * 48, &TempPlayerList.player_bone.Right_Shoulder);
      /* 左手肘 */
      GameTools::GetBoneData(Left_Elbow_Number * 48, &TempPlayerList.player_bone.Left_Elbow);
      /* 右手肘 */
      GameTools::GetBoneData(Right_Elbow_Number * 48, &TempPlayerList.player_bone.Right_Elbow);
      /* 左手腕 */
      GameTools::GetBoneData(Left_Wrist_Number * 48, &TempPlayerList.player_bone.Left_Wrist);
      /* 右手腕 */
      GameTools::GetBoneData(Right_Wrist_Number * 48, &TempPlayerList.player_bone.Right_Wrist);
      /* 左大腿 */
      GameTools::GetBoneData(Left_Thigh_Number * 48, &TempPlayerList.player_bone.Left_Thigh);
      /* 右大腿 */
      GameTools::GetBoneData(Right_Thigh_Number * 48, &TempPlayerList.player_bone.Right_Thigh);
      /* 左膝盖 */
      GameTools::GetBoneData(Left_Knee_Number * 48, &TempPlayerList.player_bone.Left_Knee);
      /* 右膝盖 */
      GameTools::GetBoneData(Right_Knee_Number * 48, &TempPlayerList.player_bone.Right_Knee);
      /* 左脚腕 */
      GameTools::GetBoneData(Left_Ankle_Number * 48, &TempPlayerList.player_bone.Left_Ankle);
      /* 右脚腕 */
      GameTools::GetBoneData(Right_Ankle_Number * 48, &TempPlayerList.player_bone.Right_Ankle);

      TempPlayerList.Angle = GameTools::toRotator(TempPlayerList.player_bone.Pelvis.Pos, TempPlayerList.player_bone.Head.Pos).Yaw;

      MainPlayerList.push_back(TempPlayerList);
    }

    for (int i = 0; i < MainIntoList.size(); i++) {
      GameTools::GetDistance(MainIntoList[i].Pos, MainUpData.SelfCoordinate, &MainIntoList[i].Distance);
      GameTools::WorldToScreen(&MainIntoList[i].ScreenPos, MainIntoList[i].Pos);
    }
  }