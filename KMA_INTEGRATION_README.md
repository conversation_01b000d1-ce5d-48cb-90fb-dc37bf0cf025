# KMA驱动集成说明

## 概述
本次修改完全替换原有驱动实现，使用KMA_KPM_v6.6.400驱动作为唯一的内核读写解决方案，提供更强大和安全的内核读写功能。

## 修改内容

### 1. 文件修改
- **jni/src/My_Tools/KernelTools.cpp**: 完全重写，仅使用KMA驱动
- **jni/include/My_Tools/KernelTools.h**: 更新头文件，清理旧函数声明
- **jni/Android.mk**: 添加KMA静态库链接
- **jni/libs/libkma_driver.a**: KMA驱动静态库（正确位置）
- **jni/src/My_Tools/driver.h**: KMA驱动头文件
- **jni/src/My_Hack/Hack.cpp**: 移除对旧驱动函数的调用

### 2. 删除的旧功能
- 删除了所有BING驱动相关代码
- 删除了橘子驱动相关代码
- 删除了`init_key`函数
- 删除了`init_oxdriver`函数
- 删除了`isBing`变量和相关逻辑

### 3. KMA驱动专用函数
```cpp
// 核心读写功能
bool driver::read(uintptr_t addr, void *buffer, size_t size);
bool driver::read_safe(uintptr_t addr, void *buffer, size_t size);
bool driver::write(uintptr_t addr, void *buffer, size_t size);

// 模板函数
template <typename T> T driver::read(uintptr_t addr);
template <typename T> T driver::read_safe(uintptr_t addr);
template <typename T> bool driver::write(uintptr_t addr, T value);

// 进程和模块管理
pid_t driver::get_pid(char *name);
pid_t driver::get_pid(char *name, char *comm);
uintptr_t driver::get_module_base(char *name, pid_t pid);
uintptr_t driver::get_module_base(pid_t pid, char *name, size_t size);

// 系统管理
void driver::cpuset(int num);
void driver::cpuset(int start, int end);
void driver::cleanup();
bool driver::initialkernel(pid_t gamepid);
```

### 4. 驱动架构
- **纯KMA驱动**: 不再有备用驱动，专注于KMA驱动的稳定性和性能
- **自动初始化**: 驱动在`initialkernel`中自动完成所有初始化
- **错误处理**: 如果KMA驱动初始化失败，函数直接返回false

### 5. 兼容性
- 保持了核心API的兼容性（read/write/get_module_base）
- 移除了旧驱动特有的函数调用
- 现有使用核心功能的代码无需修改

## 使用示例

### 基本使用
```cpp
#include "KernelTools.h"

// 初始化KMA驱动
pid_t game_pid = 1234;
if (driver::initialkernel(game_pid)) {
    // 读取内存
    int value = driver::read<int>(0x12345678);

    // 安全读取（硬件级别，推荐用于重要数据）
    int safe_value = driver::read_safe<int>(0x12345678);

    // 写入内存
    driver::write<float>(0x12345678, 1.0f);

    // 获取模块基址
    uintptr_t base = driver::get_module_base("libgame.so", game_pid);

    // 清理资源（程序结束时调用）
    driver::cleanup();
} else {
    printf("KMA驱动初始化失败！\n");
}
```

### 高级功能
```cpp
// 在初始化前设置CPU亲和性（推荐使用小核心）
driver::cpuset(0, 4);  // 使用CPU 0-4

// 获取进程PID（从内核层安全获取）
pid_t pid = driver::get_pid("com.game.package");

// 获取线程PID
pid_t thread_pid = driver::get_pid("com.game.package", "GameThread");

// 获取指定大小的模块基址（更精确）
uintptr_t base = driver::get_module_base(pid, "libgame.so", 0x100000);

// 批量读取数据
float matrix[16];
if (driver::read(base + 0x1000, &matrix, sizeof(matrix))) {
    // 处理矩阵数据
}
```

## 编译说明

### 依赖
- KMA静态库: `jni/libs/libkma_driver.a`
- 支持的架构: arm64-v8a
- NDK版本: 建议使用NDK 28+
- 最低Android版本: 根据KMA驱动要求

### 编译命令
```bash
# 使用指定的NDK路径编译
C:/Android/SDK/ndk/28.1.13356709/ndk-build.cmd

# 或者如果NDK在PATH中
ndk-build
```

### 编译输出
- 可执行文件: `libs/arm64-v8a/test.sh`
- 编译成功标志: 无错误信息，生成完整的可执行文件

## 注意事项

1. **纯KMA驱动**: 本版本完全依赖KMA驱动，不再有备用驱动
2. **安全性**: KMA驱动提供硬件级别的安全读取，建议在读取重要数据时使用`read_safe`函数
3. **性能**: `read_safe`由于不使用CPU缓存，性能相对较低，普通读取使用`read`函数
4. **多线程**: KMA驱动支持多线程，但每个线程需要创建独立的Driver对象
5. **资源管理**: 程序结束时调用`driver::cleanup()`清理资源
6. **CPU亲和性**: 建议设置程序运行在小核心上，避免影响目标应用性能
7. **初始化**: 驱动会自动设置CPU亲和性为0-4核心

## 错误处理

如果KMA驱动初始化失败：
- 检查设备是否支持KMA驱动（内核版本4.9~6.6）
- 确认静态库文件是否正确链接到`jni/libs/`目录
- 检查目标进程PID是否有效
- 查看控制台输出的错误信息

## 版本信息
- KMA驱动版本: v6.6.400
- 支持内核版本: 4.9~6.6
- 集成日期: 2025-08-05
- 编译状态: ✅ 编译成功
