//
// Created by s2049 on 24-10-6.
//

#include "LinuxSystem_Tools.h"
#include <cstring>
#include <dirent.h>
#include <elf.h>
#include <fcntl.h>
#include <fstream>
#include <iostream>
#include <sys/system_properties.h>
#include <sys/uio.h>
#include <sys/wait.h>
#include <unistd.h>

#include <sstream>

using namespace std;

string LinuxSystem_Tools::libcPath = "";
string LinuxSystem_Tools::linkerPath = "";
string LinuxSystem_Tools::libDLPath = "";
DeviceArchitecture LinuxSystem_Tools::arch = DeviceArchitecture::Unknown;
int LinuxSystem_Tools::sdkVersion = 0;

void LinuxSystem_Tools::setupSystemLibs() {
  char sdkVersionStr[32];
  char sysArchitecture[32];
  __system_property_get("ro.build.version.sdk", sdkVersionStr);
  __system_property_get("ro.product.cpu.abi", sysArchitecture);
  sdkVersion = stoi(sdkVersionStr);

  if (strcmp(sysArchitecture, "x86_64") == 0) {
    arch = DeviceArchitecture::X86_64;
    libcPath = "/system/lib64/libc.so";
    linkerPath = "/system/bin/linker64";
    libDLPath = "/system/lib64/libdl.so";
  } else if (strcmp(sysArchitecture, "x86") == 0) {
    arch = DeviceArchitecture::X86;
    libcPath = "/system/lib/libc.so";
    linkerPath = "/system/bin/linker";
    libDLPath = "/system/lib/arm/nb/libdl.so";
  } else if (strcmp(sysArchitecture, "arm64-v8a") == 0) {
    arch = DeviceArchitecture::Arm64V8a;
    if (sdkVersion >= 29) { // Android 10
      libcPath = "/apex/com.android.runtime/lib64/bionic/libc.so";
      linkerPath = "/apex/com.android.runtime/bin/linker64";
      libDLPath = "/apex/com.android.runtime/lib64/bionic/libdl.so";
    } else {
      libcPath = "/system/lib64/libc.so";
      linkerPath = "/system/bin/linker64";
      libDLPath = "/system/lib64/libdl.so";
    }
  } else if (strcmp(sysArchitecture, "armeabi-v7a") == 0) {
    arch = DeviceArchitecture::ArmeabiV7a;
  } else {
    arch = DeviceArchitecture::Unknown;
  }
}

int LinuxSystem_Tools::isSELinuxEnabled() {
  ifstream file("/proc/filesystems");
  string line;
  while (getline(file, line)) {
    if (line.find("selinuxfs") != string::npos) {
      return 1;
    }
  }
  return 0;
}

// 开启SELinux宽容模式
void LinuxSystem_Tools::disableSELinux() {
  ifstream file("/proc/mounts");
  string line;
  while (getline(file, line)) {
    size_t pos = line.find("selinuxfs");
    if (pos != string::npos) {
      istringstream iss(line);
      string mountPoint;
      iss >> mountPoint >> mountPoint;

      string selinuxPath = mountPoint + "/enforce";
      ofstream selinuxFile(selinuxPath);
      if (selinuxFile.is_open()) {
        selinuxFile << '0'; // 0 = Permissive
      }
      break;
    }
  }
}

// 关闭SELinux宽容模式
void LinuxSystem_Tools::enableSELinux() {
  ifstream file("/proc/mounts");
  string line;
  while (getline(file, line)) {
    size_t pos = line.find("selinuxfs");
    if (pos != string::npos) {
      istringstream iss(line);
      string mountPoint;
      iss >> mountPoint >> mountPoint;

      string selinuxPath = mountPoint + "/enforce";
      ofstream selinuxFile(selinuxPath);
      if (selinuxFile.is_open()) {
        selinuxFile << '1';
        selinuxFile.close();
      } else {
        cerr << "Failed to open SELinux enforce file." << std::endl;
      }
      break;
    }
  }
}

void LinuxSystem_Tools::launchApp(const string &appLaunchActivity) {
  string command = "am start " + appLaunchActivity;
  system(command.c_str());
}

void *LinuxSystem_Tools::getModuleBaseAddr(pid_t pid,
                                           const string &moduleName) {
  uintptr_t moduleBaseAddr = 0;
  string mapsPath = "/proc/" + to_string(pid) + "/maps";
  ifstream mapsFile(mapsPath);
  string line;

  while (getline(mapsFile, line)) {
    if (line.find(moduleName) != string::npos) {
      istringstream iss(line);
      string address;
      getline(iss, address, '-');
      moduleBaseAddr = stoull(address, nullptr, 16);

      if (moduleBaseAddr == 0x8000) {
        moduleBaseAddr = 0;
      }

      break;
    }
  }

  return reinterpret_cast<void *>(moduleBaseAddr);
}

void *LinuxSystem_Tools::getRemoteFuncAddr(pid_t pid, const string &moduleName, void *localFuncAddr) {
  void *localModuleAddr = getModuleBaseAddr(getpid(), moduleName);
  void *remoteModuleAddr = getModuleBaseAddr(pid, moduleName);
  uintptr_t remoteFuncAddr = reinterpret_cast<uintptr_t>(localFuncAddr) -
                             reinterpret_cast<uintptr_t>(localModuleAddr) +
                             reinterpret_cast<uintptr_t>(remoteModuleAddr);

  return reinterpret_cast<void *>(remoteFuncAddr);
}