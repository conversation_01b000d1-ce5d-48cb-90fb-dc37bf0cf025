//
// Created by s2049 on 7/2/2024.
//

#ifndef GUI_MAX_CONFIGMANAGER_H
#define GUI_MAX_CONFIGMANAGER_H

#include "VectorTools.h"
#include "imgui_internal.h"
#include "json.hpp"
#include <string>
#include <unordered_map>
#include <vector>

struct Config {
  bool DEBUG = false;
  int RunFPS = 60;

  /* 绘制开关区 */
  bool isShowBox = false;
  bool isShowBone = true;
  bool isShowDistance = true;
  bool isShowTeamID = true;
  bool isShowWeaponID = true;
  bool isShowName = true;
  bool isShowHealth = true;
  bool isShowLine = true;
  bool isShowWorring = true;
  bool isShowarticle = true;
  bool isShowBot = true;
  bool isRadar = true;

  float RadarX = 350;
  float RadarY = 350;
  float RadarSize = 300;
  bool isShowVehicles = true;                       // 显示载具
  ImColor VehicleColor = ImColor(0, 255, 255, 255); // 载具显示颜色
  float VehicleNameSize = 24.0f;                    // 载具名称大小

  // 物资显示设置
  std::unordered_map<int, bool> ShowItemTypes = {
      {1, true},  // 投掷物类
      {2, true},  // 载具类
      {3, true},  // 防具类
      {4, true},  // 道具类
      {5, true},  // 盒子
      {6, true},  // 药品
      {7, true},  // 子弹
      {8, true},  // 762枪械
      {9, true},  // 556枪械
      {10, true}, // 冲锋枪
      {11, true}, // 霰弹枪
      {12, true}, // 狙击枪
      {13, true}, // 其他
      {14, true}, // 步枪配件
      {15, true}, // 倍镜
      {16, true}  // 地铁宝箱
  };

  // 绘制物资开关
  bool isSpoils = true;  // 盒子
  bool isGrenade = true; // 手雷预警

  /* 杂项参数 */
  int articleMode = 0;
  float showBoneDistance = 350.0f;
  float showBotBoneDistance = 350.0f;
  float aimDistance = 350.0f;
  float aimBotDistance = 350.0f;
  float NotaimDistance = 350.0f;
  float NotaimBotDistance = 350.0f;

  /* 自瞄开关区 */
  bool isFree = false;
  bool isAimbot = false;
  bool isNotAimSmoke = false;
  bool isNotAimRobot = false;
  bool isSmoothing = false;
  bool ScreenOrientation = false;
  float Range = 350.0f;
  bool isRange = true;
  float Speed = 0.45f;
  float Speed_X = 0.06f;
  float Speed_Y = 0.10f;
  float accuracy_X = 0.15f;
  float accuracy_Y = 0.15f;
  int Random_Speed = 3;
  float Down = 2.80f;
  float Squat_Down = 0.65f;
  float Lying_Down = 0.30f;
  float Predict = 0.125f;
  int Defer = 2;
  int LockMode = 0; // 开火 开镜 开火且开镜
  int LockPos = 0;  // 头 胸 腰
  bool isContinued = false;
  bool isKnockdown = false;
  bool isTouch = false;
  bool isFTouch = false;
  float TouchPosx = 0.8;
  float TouchPosy = 0.6;
  float TouchRange = 0.04;
  float TouchFPS = 900;

  /* 定义自定义颜色 */
  ImColor TouchingColor = ImColor(255, 0, 0, 150);
  ;
  ImColor BoxblackColor = ImColor(255, 0, 0, 25);
  ImColor BotBoxblackColor = ImColor(255, 0, 0, 25);
  ImColor LineColor = ImColor(255, 0, 0, 255);
  ImColor BotLineColor = ImColor(255, 255, 255, 255);
  ImColor BoneColor = ImColor(255, 0, 0, 255);
  ImColor BotBoneColor = ImColor(255, 255, 255, 255);
  ImColor WarningColor = ImColor(255, 0, 0, 255);
  ImColor BotWarningColor = ImColor(255, 255, 255, 255);

  /* 定义自定义大小 */
  float BoxSize = 1.5f;
  float BotBoxSize = 1.5f;
  float LineSize = 1.5f;
  float BotLineSize = 1.5f;
  float BoneSize = 2.5f;
  float BotBoneSize = 2.5f;
  ImVec2 IslandSize = ImVec2(400, 80);

  // 序列化函数
  void to_json(nlohmann::json &j) const {
    j = nlohmann::json{
        {"DEBUG", DEBUG},
        {"RunFPS", RunFPS},
        {"isShowBox", isShowBox},
        {"isShowBone", isShowBone},
        {"isShowDistance", isShowDistance},
        {"isShowTeamID", isShowTeamID},
        {"isShowWeaponID", isShowWeaponID},
        {"isShowName", isShowName},
        {"isShowHealth", isShowHealth},
        {"isShowLine", isShowLine},
        {"isShowWorring", isShowWorring},
        {"isShowarticle", isShowarticle},
        {"isShowBot", isShowBot},
        {"isRadar", isRadar},
        {"RadarX", RadarX},
        {"RadarY", RadarY},
        {"RadarSize", RadarSize},
        {"isShowVehicles", isShowVehicles},
        {"VehicleColor", {VehicleColor.Value.x, VehicleColor.Value.y, VehicleColor.Value.z, VehicleColor.Value.w}},
        {"VehicleNameSize", VehicleNameSize},
        {"isSpoils", isSpoils},
        {"isGrenade", isGrenade},
        {"articleMode", articleMode},
        {"showBoneDistance", showBoneDistance},
        {"showBotBoneDistance", showBotBoneDistance},
        {"aimDistance", aimDistance},
        {"aimBotDistance", aimBotDistance},
        {"NotaimDistance", NotaimDistance},
        {"NotaimBotDistance", NotaimBotDistance},
        {"isFree", isFree},
        {"isAimbot", isAimbot},
        {"isNotAimSmoke", isNotAimSmoke},
        {"isNotAimRobot", isNotAimRobot},
        {"isSmoothing", isSmoothing},
        {"ScreenOrientation", ScreenOrientation},
        {"Range", Range},
        {"isRange", isRange},
        {"Speed", Speed},
        {"Speed_X", Speed_X},
        {"Speed_Y", Speed_Y},
        {"accuracy_X", accuracy_X},
        {"accuracy_Y", accuracy_Y},
        {"Random_Speed", Random_Speed},
        {"Down", Down},
        {"Squat_Down", Squat_Down},
        {"Lying_Down", Lying_Down},
        {"Predict", Predict},
        {"Defer", Defer},
        {"LockMode", LockMode},
        {"LockPos", LockPos},
        {"isContinued", isContinued},
        {"isKnockdown", isKnockdown},
        {"isTouch", isTouch},
        {"isFTouch", isFTouch},
        {"TouchPosx", TouchPosx},
        {"TouchPosy", TouchPosy},
        {"TouchRange", TouchRange},
        {"TouchFPS", TouchFPS},
        {"TouchingColor", {TouchingColor.Value.x, TouchingColor.Value.y, TouchingColor.Value.z, TouchingColor.Value.w}},
        {"BoxblackColor", {BoxblackColor.Value.x, BoxblackColor.Value.y, BoxblackColor.Value.z, BoxblackColor.Value.w}},
        {"BotBoxblackColor", {BotBoxblackColor.Value.x, BotBoxblackColor.Value.y, BotBoxblackColor.Value.z, BotBoxblackColor.Value.w}},
        {"LineColor", {LineColor.Value.x, LineColor.Value.y, LineColor.Value.z, LineColor.Value.w}},
        {"BotLineColor", {BotLineColor.Value.x, BotLineColor.Value.y, BotLineColor.Value.z, BotLineColor.Value.w}},
        {"BoneColor", {BoneColor.Value.x, BoneColor.Value.y, BoneColor.Value.z, BoneColor.Value.w}},
        {"BotBoneColor", {BotBoneColor.Value.x, BotBoneColor.Value.y, BotBoneColor.Value.z, BotBoneColor.Value.w}},
        {"WarningColor", {WarningColor.Value.x, WarningColor.Value.y, WarningColor.Value.z, WarningColor.Value.w}},
        {"BotWarningColor", {BotWarningColor.Value.x, BotWarningColor.Value.y, BotWarningColor.Value.z, BotWarningColor.Value.w}},
        {"BoxSize", BoxSize},
        {"BotBoxSize", BotBoxSize},
        {"LineSize", LineSize},
        {"BotLineSize", BotLineSize},
        {"BoneSize", BoneSize},
        {"BotBoneSize", BotBoneSize},
        {"IslandSize", {IslandSize.x, IslandSize.y}}};

    // 保存物资类型显示设置
    nlohmann::json itemTypes;
    for (const auto &pair : ShowItemTypes) {
      itemTypes[std::to_string(pair.first)] = pair.second;
    }
    j["ShowItemTypes"] = itemTypes;
  }

  void from_json(const nlohmann::json &j) {
    try {
      j.at("DEBUG").get_to(DEBUG);
    } catch (nlohmann::json::exception &e) {
      DEBUG = false;
    }
    try {
      j.at("RunFPS").get_to(RunFPS);
    } catch (nlohmann::json::exception &e) {
      RunFPS = 60;
    }
    try {
      j.at("isShowBox").get_to(isShowBox);
    } catch (nlohmann::json::exception &e) {
      isShowBox = false;
    }
    try {
      j.at("isShowBone").get_to(isShowBone);
    } catch (nlohmann::json::exception &e) {
      isShowBone = false;
    }
    j.at("isShowDistance").get_to(isShowDistance);
    j.at("isShowTeamID").get_to(isShowTeamID);
    j.at("isShowWeaponID").get_to(isShowWeaponID);
    j.at("isShowName").get_to(isShowName);
    j.at("isShowHealth").get_to(isShowHealth);
    j.at("isShowLine").get_to(isShowLine);
    j.at("isShowWorring").get_to(isShowWorring);
    j.at("isShowarticle").get_to(isShowarticle);
    try {
      j.at("isShowBot").get_to(isShowBot);
    } catch (nlohmann::json::exception &e) {
      isShowBot = false;
    }
    j.at("isRadar").get_to(isRadar);
    j.at("RadarX").get_to(RadarX);
    j.at("RadarY").get_to(RadarY);
    j.at("RadarSize").get_to(RadarSize);

    // 载具设置
    try {
      j.at("isShowVehicles").get_to(isShowVehicles);
    } catch (nlohmann::json::exception &e) {
      isShowVehicles = true;
    }

    if (j.count("VehicleColor")) {
      std::vector<float> vehicleColorData = j.at("VehicleColor").get<std::vector<float>>();
      VehicleColor = ImColor(vehicleColorData[0], vehicleColorData[1], vehicleColorData[2], vehicleColorData[3]);
    }

    try {
      j.at("VehicleNameSize").get_to(VehicleNameSize);
    } catch (nlohmann::json::exception &e) {
      VehicleNameSize = 24.0f;
    }

    // 加载物资类型显示设置
    try {
      if (j.count("ShowItemTypes")) {
        auto itemTypes = j.at("ShowItemTypes");
        for (auto it = itemTypes.begin(); it != itemTypes.end(); ++it) {
          int typeId = std::stoi(it.key());
          ShowItemTypes[typeId] = it.value().get<bool>();
        }
      }
    } catch (nlohmann::json::exception &e) {
      // 保持默认值
    }

    j.at("isSpoils").get_to(isSpoils);
    j.at("isGrenade").get_to(isGrenade);
    j.at("articleMode").get_to(articleMode);
    j.at("showBoneDistance").get_to(showBoneDistance);
    j.at("showBotBoneDistance").get_to(showBotBoneDistance);
    j.at("aimDistance").get_to(aimDistance);
    j.at("aimBotDistance").get_to(aimBotDistance);
    j.at("NotaimDistance").get_to(NotaimDistance);
    j.at("NotaimBotDistance").get_to(NotaimBotDistance);
    j.at("isFree").get_to(isFree);
    j.at("isAimbot").get_to(isAimbot);
    try {
      j.at("isNotAimSmoke").get_to(isNotAimSmoke);
    } catch (nlohmann::json::exception &e) {
      isNotAimSmoke = false;
    }
    j.at("isNotAimRobot").get_to(isNotAimRobot);
    j.at("isSmoothing").get_to(isSmoothing);
    j.at("ScreenOrientation").get_to(ScreenOrientation);
    j.at("Range").get_to(Range);
    j.at("isRange").get_to(isRange);
    j.at("Speed").get_to(Speed);
    j.at("Speed_X").get_to(Speed_X);
    j.at("Speed_Y").get_to(Speed_Y);
    try {
      j.at("accuracy_X").get_to(accuracy_X);
    } catch (nlohmann::json::exception &e) {
      accuracy_X = 0.15f; // 假设默认值为 1.0f
    }

    try {
      j.at("accuracy_Y").get_to(accuracy_Y);
    } catch (nlohmann::json::exception &e) {
      accuracy_Y = 0.15f; // 假设默认值为 1.0f
    }
    j.at("Random_Speed").get_to(Random_Speed);
    j.at("Down").get_to(Down);
    j.at("Squat_Down").get_to(Squat_Down);
    j.at("Lying_Down").get_to(Lying_Down);
    j.at("Predict").get_to(Predict);
    j.at("Defer").get_to(Defer);
    j.at("LockMode").get_to(LockMode);
    j.at("LockPos").get_to(LockPos);
    j.at("isContinued").get_to(isContinued);
    j.at("isKnockdown").get_to(isKnockdown);
    j.at("isTouch").get_to(isTouch);
    j.at("isFTouch").get_to(isFTouch);
    j.at("TouchPosx").get_to(TouchPosx);
    j.at("TouchPosy").get_to(TouchPosy);
    j.at("TouchRange").get_to(TouchRange);
    j.at("TouchFPS").get_to(TouchFPS);

    if (j.count("TouchingColor")) {
      std::vector<float> touchingColorData = j.at("TouchingColor").get<std::vector<float>>();
      TouchingColor = ImColor(touchingColorData[0], touchingColorData[1], touchingColorData[2], touchingColorData[3]);
    }

    if (j.count("BoxblackColor")) {
      std::vector<float> boxblackColorData = j.at("BoxblackColor").get<std::vector<float>>();
      BoxblackColor = ImColor(boxblackColorData[0], boxblackColorData[1], boxblackColorData[2], boxblackColorData[3]);
    }

    if (j.count("BotBoxblackColor")) {
      std::vector<float> botBoxblackColorData = j.at("BotBoxblackColor").get<std::vector<float>>();
      BotBoxblackColor = ImColor(botBoxblackColorData[0], botBoxblackColorData[1], botBoxblackColorData[2], botBoxblackColorData[3]);
    }

    if (j.count("LineColor")) {
      std::vector<float> lineColorData = j.at("LineColor").get<std::vector<float>>();
      LineColor = ImColor(lineColorData[0], lineColorData[1], lineColorData[2], lineColorData[3]);
    }

    if (j.count("BotLineColor")) {
      std::vector<float> botLineColorData = j.at("BotLineColor").get<std::vector<float>>();
      BotLineColor = ImColor(botLineColorData[0], botLineColorData[1], botLineColorData[2], botLineColorData[3]);
    }

    if (j.count("BoneColor")) {
      std::vector<float> boneColorData = j.at("BoneColor").get<std::vector<float>>();
      BoneColor = ImColor(boneColorData[0], boneColorData[1], boneColorData[2], boneColorData[3]);
    }

    if (j.count("BotBoneColor")) {
      std::vector<float> botBoneColorData = j.at("BotBoneColor").get<std::vector<float>>();
      BotBoneColor = ImColor(botBoneColorData[0], botBoneColorData[1], botBoneColorData[2], botBoneColorData[3]);
    }

    if (j.count("WarningColor")) {
      std::vector<float> warningColorData = j.at("WarningColor").get<std::vector<float>>();
      WarningColor = ImColor(warningColorData[0], warningColorData[1], warningColorData[2], warningColorData[3]);
    }

    if (j.count("BotWarningColor")) {
      std::vector<float> botWarningColorData = j.at("BotWarningColor").get<std::vector<float>>();
      BotWarningColor = ImColor(botWarningColorData[0], botWarningColorData[1], botWarningColorData[2], botWarningColorData[3]);
    }

    j.at("BoxSize").get_to(BoxSize);
    j.at("BotBoxSize").get_to(BotBoxSize);
    j.at("LineSize").get_to(LineSize);
    j.at("BotLineSize").get_to(BotLineSize);
    j.at("BoneSize").get_to(BoneSize);
    j.at("BotBoneSize").get_to(BotBoneSize);
    if (j.count("IslandSize")) {
      std::vector<float> IslandSizeData = j.at("IslandSize").get<std::vector<float>>();
      IslandSize = ImVec2(IslandSizeData[0], IslandSizeData[1]);
    }
  }
};

namespace ConfigManager {
extern Config Settings;                                 // 当前加载的配置
extern std::unordered_map<std::string, Config> configs; // 存储配置名和配置对象的映射
extern std::string defaultConfigName;                   // 默认配置的名称
extern std::string currentConfigName;                   // 当前配置的名称
extern int configsCount;                                // 配置数量

/**
 * @brief 保存配置文件
 * @return 保存成功返回true，否则返回false
 */
bool saveMapConfig();

/**
 * @brief 加载配置文件
 * @return 加载成功返回true，若未找到配置文件false
 */
bool loadMapConfig();

/**
 * @brief 加载ConfigManager
 * @return 加载成功返回true，若未找到配置文件false
 */
bool LoadConfigManager();

/**
 * @brief 保存ConfigManager
 * @return 保存成功返回true，若失败false
 */
bool SaveConfigManager();

/**
 * @brief 保存新配置
 * @param name 配置名称
 * @param config 配置对象
 * @return 保存成功返回true，否则返回false
 */
bool SaveConfig(const std::string &name, const Config &config);

/**
 * @brief 添加新配置
 * @param name 配置名称
 * @param config 配置对象
 * @return 添加成功返回true，若配置已存在则返回false
 */
bool AddConfig(const std::string &name, const Config &config);

/**
 * @brief 移除配置
 * @param name 要移除的配置名称
 * @return 移除成功返回true，若配置不存在则返回false
 */
bool RemoveConfig(const std::string &name);

/**
 * @brief 获取配置
 * @param name 配置名称
 * @return 返回配置对象的指针，若配置不存在返回nullptr
 */
Config GetConfig(const std::string &name);

/**
 * @brief 设置默认配置
 * @param name 要设置为默认配置的配置名称
 * @return 设置成功返回true，若配置不存在则返回false
 */
bool SetDefaultConfig(const std::string &name);

/**
 * @brief 获取默认配置
 * @return 返回默认配置对象的指针，若默认配置不存在返回nullptr
 */
Config GetDefaultConfig();

/**
 * @brief 切换当前配置
 * @param name 要设置为当前配置的配置名称
 * @return 设置成功返回true，若配置不存在则返回false
 */
bool SetCurrentConfig(const std::string &name);

/**
 * @brief 获取当前配置
 * @return 返回当前配置对象的指针，若当前配置不存在返回nullptr
 */
Config GetCurrentConfig();

/**
 * @brief 获取配置数量
 * @return 返回配置数量
 */
int GetConfigCount();

/**
 * 恢复为默认配置
 * 将当前配置设置为默认配置
 */
void ResetToDefaultConfig();

/**
 * @brief 获取配置名称列表
 * @return 返回一个包含所有配置名称的vector
 */
std::vector<std::string> GetConfigNames();

/**
 * @brief 获取当前配置名称
 * @return 返回当前配置名称
 */
std::string GetCurrentConfigName();

/**
 * @brief 获取默认配置名称
 * @return 返回默认配置名称
 */
std::string GetDefaultConfigName();

/**
 * @brief 构造函数
 * @brief 创建ConfigManager对象时，初始化一个名为"default"的默认配置，并将其添加到configs中
 */
void ConfigManager_Init();
}; // namespace ConfigManager

#endif // GUI_MAX_CONFIGMANAGER_H
