./obj/local/arm64-v8a/objs/test.sh/src/Android_touch/TouchHelperA.o: \
  jni/src/Android_touch/TouchHelperA.cpp jni/include/ImGui/imgui.h \
  jni/include/ImGui/imconfig.h jni/include/My_Tools/timer.h \
  jni/include/ImGui/imgui_internal.h jni/include/ImGui/imstb_textedit.h \
  jni/include/Android_touch/TouchHelperA.h \
  jni/include/My_Tools/VectorTools.h \
  jni/include/My_Tools/ConfigManager.h jni/include/Android_draw/json.hpp \
  jni/include/Android_draw/draw.h \
  jni/include/native_surface/ANativeWindowCreator.h \
  jni/include/Android_draw/AndroidImgui.h \
  jni/include/ImGui/imgui_Custom.h \
  jni/include/Android_my_imgui/my_imgui.h jni/include/My_Tools/driver.h
jni/include/ImGui/imgui.h:
jni/include/ImGui/imconfig.h:
jni/include/My_Tools/timer.h:
jni/include/ImGui/imgui_internal.h:
jni/include/ImGui/imstb_textedit.h:
jni/include/Android_touch/TouchHelperA.h:
jni/include/My_Tools/VectorTools.h:
jni/include/My_Tools/ConfigManager.h:
jni/include/Android_draw/json.hpp:
jni/include/Android_draw/draw.h:
jni/include/native_surface/ANativeWindowCreator.h:
jni/include/Android_draw/AndroidImgui.h:
jni/include/ImGui/imgui_Custom.h:
jni/include/Android_my_imgui/my_imgui.h:
jni/include/My_Tools/driver.h:
