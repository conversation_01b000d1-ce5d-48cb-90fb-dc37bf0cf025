#include <cmath>
#include <iostream>
#include <linux/input.h>
#include <sys/epoll.h>

#include "AimBot.h"
#include "ConfigManager.h"
#include "GameTools.h"
#include "Hack.h"
#include "XCJDrawList.h"
#include "draw.h"
#include "dynamic_Island.h"

#include "KernelTools.h"
#include "font.h"
#include "logo_png_data.h"
// 定义PI常量
#ifndef IM_PI
#define IM_PI 3.14159265358979323846f
#endif

using namespace std;

bool windowInit_main = true;
bool permeate_record = false;
bool permeate_record_ini = false;
struct Last_ImRect LastCoordinate = {0, 0, 0, 0};

static uint32_t orientation = -1;
ANativeWindow *window;
// 屏幕信息
android::ANativeWindowCreator::DisplayInfo displayInfo;
// 窗口信息
ImGuiWindow *g_window;
// 绝对屏幕X _ Y
int abs_ScreenX, abs_ScreenY;
// 当前屏幕X _ Y
int screen_x, screen_y;
// 窗口渲染大小 X _ Y
int native_window_screen_x, native_window_screen_y;
// 自定义屏幕X _ Y
int screen_x_set, screen_y_set;
// 是否设置屏幕信息
bool is_set_screen;
// 悬浮窗图片
BaseTexData *logo_image;
// 通知
DynamicIsland *notificationManager;

std::unique_ptr<AndroidImgui> graphics;

bool M_Android_LoadFont(float SizePixels) {
  ImGuiIO &io = ImGui::GetIO();

  ImFontConfig font_cfg;
  font_cfg.FontDataOwnedByAtlas = false;
  io.Fonts->AddFontFromMemoryTTF((void *)FontFile, Fontsize, SizePixels, &font_cfg, io.Fonts->GetGlyphRangesChineseFull());

  return true;
}

void init_My_drawdata() {
  M_Android_LoadFont(25.0f); // 加载字体(还有图标)

  LastCoordinate.Size_x = 1000;
  LastCoordinate.Size_y = 600;

  ::logo_image = graphics->LoadTextureFromMemory((void *)logo_data, sizeof(logo_data));
}

void screen_config() {
  ::displayInfo = android::ANativeWindowCreator::GetDisplayInfo();
  if (is_set_screen) {
    ::displayInfo.width = screen_x_set;
    ::displayInfo.height = screen_y_set;
    ::displayInfo.orientation = 1;
  }
  screen_x = displayInfo.width;
  screen_y = displayInfo.height;

  GameTools::HalfscreenSize.x = screen_x / 2;
  GameTools::HalfscreenSize.y = screen_y / 2;

  resolution_information.ScreenWidth = displayInfo.width;
  resolution_information.ScreenHeiht = displayInfo.height;
  if (displayInfo.orientation != resolution_information.Orientation) {
    touch_information.TouchOrientationControl = true;
    resolution_information.Orientation = displayInfo.orientation;
  }
}

void drawBegin() {
  if (::permeate_record_ini) {
    LastCoordinate.Pos_x = ::g_window->Pos.x;
    LastCoordinate.Pos_y = ::g_window->Pos.y;
    LastCoordinate.Size_x = ::g_window->Size.x;
    LastCoordinate.Size_y = ::g_window->Size.y;

    graphics->Shutdown();
    android::ANativeWindowCreator::Destroy(::window);
    ::window = android::ANativeWindowCreator::Create("androidxList", native_window_screen_x, native_window_screen_y, permeate_record);
    graphics->Init_Render(::window, native_window_screen_x, native_window_screen_y);
    ::init_My_drawdata(); // 初始化绘制数据
  }

  screen_config();
  if (::orientation != displayInfo.orientation) {
    ::orientation = displayInfo.orientation;
    if (g_window != nullptr) {
      g_window->Pos.x = 100;
      g_window->Pos.y = 125;
    }
  }
}

float transparent = 127 / 255.f;
ImVec4 arr[] = {{144 / 255.f, 238 / 255.f, 144 / 255.f, transparent},
                {135 / 255.f, 206 / 255.f, 255 / 255.f, transparent},
                {255 / 255.f, 0 / 255.f, 0 / 255.f, transparent},
                {0 / 255.f, 255 / 255.f, 0 / 255.f, transparent},
                {0 / 255.f, 255 / 255.f, 127 / 255.f, transparent},
                {255 / 255.f, 182 / 255.f, 193 / 255.f, transparent},
                {218 / 255.f, 112 / 255.f, 214 / 255.f, transparent},
                {248 / 255.f, 248 / 255.f, 255 / 255.f, transparent},
                {0 / 255.f, 255 / 255.f, 255 / 255.f, transparent},
                {255 / 255.f, 165 / 255.f, 0 / 255.f, transparent},
                {153 / 255.f, 204 / 255.f, 255 / 255.f, transparent},
                {204 / 255.f, 255 / 255.f, 153 / 255.f, transparent},
                {255 / 255.f, 255 / 255.f, 153 / 255.f, transparent},
                {255 / 255.f, 153 / 255.f, 153 / 255.f, transparent},
                {153 / 255.f, 153 / 255.f, 204 / 255.f, transparent},
                {204 / 255.f, 204 / 255.f, 204 / 255.f, transparent},
                {102 / 255.f, 204 / 255.f, 153 / 255.f, transparent},
                {255 / 255.f, 102 / 255.f, 0 / 255.f, transparent},
                {102 / 255.f, 204 / 255.f, 204 / 255.f, transparent},
                {153 / 255.f, 204 / 255.f, 255 / 255.f, transparent}};
int length = sizeof(arr) / 16;

void HideVolumeKeys(bool *is_finish) {
  DIR *dir = opendir("/dev/input/");
  if (dir == nullptr) {
    perror("未找到输入设备");
    exit(EXIT_FAILURE);
  }

  int count = 0;
  dirent *ptr = nullptr;
  while ((ptr = readdir(dir)) != nullptr) {
    if (strstr(ptr->d_name, "event")) {
      count++;
    }
  }
  closedir(dir);

  if (count == 0) {
    printf("未找到 event 输入设备\n");
    return;
  }

  int *fdArray = (int *)malloc(count * sizeof(int));
  if (fdArray == nullptr) {
    perror("内存分配失败");
    exit(EXIT_FAILURE);
  }

  // 打开所有 event 设备
  for (int i = 0; i < count; i++) {
    char temp[128];
    sprintf(temp, "/dev/input/event%d", i);
    fdArray[i] = open(temp, O_RDONLY | O_NONBLOCK);
    if (fdArray[i] < 0) {
      perror("无法打开输入设备");
      continue;
    }
  }

  int epoll_fd = epoll_create1(0);
  if (epoll_fd == -1) {
    perror("epoll_create1 失败");
    exit(EXIT_FAILURE);
  }

  struct epoll_event *events = (struct epoll_event *)malloc(count * sizeof(struct epoll_event));
  if (events == nullptr) {
    perror("内存分配失败");
    exit(EXIT_FAILURE);
  }

  for (int i = 0; i < count; i++) {
    struct epoll_event ev;
    ev.events = EPOLLIN;
    ev.data.fd = fdArray[i];
    if (epoll_ctl(epoll_fd, EPOLL_CTL_ADD, fdArray[i], &ev) == -1) {
      perror("epoll_ctl 添加文件描述符失败");
    }
  }

  bool finish = *is_finish;
  while (!finish) {
    finish = *is_finish;
    int nfds = epoll_wait(epoll_fd, events, count, 100); // 100 毫秒超时
    if (nfds == -1) {
      perror("epoll_wait 失败");
      continue;
    }

    for (int i = 0; i < nfds; i++) {
      input_event rev[16]{};
      ssize_t n = read(events[i].data.fd, &rev, sizeof(rev));
      size_t count = size_t(n) / sizeof(input_event);
      for (int j = 0; j < count; j++) {
        input_event ev = rev[j];
        if (ev.type == EV_KEY) {
          if (ev.code == KEY_VOLUMEDOWN && ev.value == 1) {
            BallSwitch = false;
            NowBallSwitch = false;
            // dynamic_Island_XCJ::SetWindowFlage(false);
          } else if (ev.code == KEY_VOLUMEUP && ev.value == 1) {
            NowBallSwitch = true;
            BallSwitch = true;
            // dynamic_Island_XCJ::SetWindowFlage(true);
          }
        }
      }
    }
  }

  for (int i = 0; i < count; i++) {
    if (fdArray[i] >= 0) {
      close(fdArray[i]);
    }
  }
  free(fdArray);
  free(events);
  close(epoll_fd);
}
bool BallSwitch = false;
bool NowBallSwitch = false;

void Layout_tick_UI(bool *main_thread_flag) {
  static int style_idx = 0;
  static int GameSetToUI = GameSetAPI::ChinaPUBG;
  static const std::vector<std::string> menu_items = {"人物", "自瞄", "物资", "设置"};
  enum MenuTab {
    Draw,
    Aimbot,
    into,
    Set
  };

  static int currentMenuTab = MenuTab::Draw;

  if (NowBallSwitch) {
    ImGui::Begin("小菜鸡", &BallSwitch, ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NoResize);
    {
      if (::permeate_record_ini || ::windowInit_main) {
        ImGui::SetWindowPos({LastCoordinate.Pos_x, LastCoordinate.Pos_y});
        ImGui::SetWindowSize({LastCoordinate.Size_x, LastCoordinate.Size_y});
        permeate_record_ini = false;
        windowInit_main = false;
      }
      if (ImGui::BeginChild("##左侧菜单标题", ImVec2(200, -1), false, ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NavFlattened))
        ;
      {
        ImGui::ItemSize(ImVec2(0, 10));
        ImGui::RenderVerticalMenu(menu_items, currentMenuTab);
      }
      ImGui::EndChild();

      ImGui::SameLine();
      ImGui::SeparatorEx(ImGuiSeparatorFlags_Vertical);
      ImGui::SameLine();

      ImGui::BeginChild("##UI::Page");
      ImGui::ItemSize(ImVec2(0, 4));

      ImGui::ItemSize(ImVec2(0, 2));
      if (ImGui::BeginChild("TabLayout", ImVec2(ImGui::GetWindowWidth(), -1))) {
        switch (currentMenuTab) {
        case MenuTab::Draw:
          DrawTabContents(GameSetToUI);
          break;
        case MenuTab::Aimbot:
          AimbotTabContents();
          break;
        case MenuTab::into:
          InfoTabContents();
          break;
        case MenuTab::Set:
          SetTabContents(main_thread_flag, style_idx);
          break;
        default:
          std::cerr << "invalid MenuTab" << std::endl;
        }
      }
      ImGui::EndChild();
      g_window = ImGui::GetCurrentWindow();
      ImGui::End();
    }
  }
}

void DrawTabContents(int &GameSetToUI) {
  if (GameTools::IsStartRead) {
    if (ImGui::Button("注销读写", {-1, 80})) {
      GameTools::IsStartRead = false;
    }
  } else {
    if (ImGui::Button("初始化", {-1, 80})) {
      GameOffset::InitToGameOffset(GameSetToUI);
      GameTools::IsStartRead = Init_StratHack();
    }
  }
  ImGui::Text("人物透视选项");
  if (ImGui::BeginTable("Table1", 2)) {
    ImGui::TableNextColumn();
    ImGui::M_SwitchButton("方框", &ConfigManager::Settings.isShowBox);
    ImGui::TableNextColumn();
    ImGui::M_SwitchButton("骨骼", &ConfigManager::Settings.isShowBone);
    ImGui::TableNextColumn();
    ImGui::M_SwitchButton("射线", &ConfigManager::Settings.isShowLine);
    ImGui::TableNextColumn();
    ImGui::M_SwitchButton("距离", &ConfigManager::Settings.isShowDistance);
    ImGui::TableNextColumn();
    ImGui::M_SwitchButton("血量", &ConfigManager::Settings.isShowHealth);
    ImGui::TableNextColumn();
    ImGui::M_SwitchButton("名称", &ConfigManager::Settings.isShowName);
    ImGui::TableNextColumn();
    ImGui::M_SwitchButton("预警", &ConfigManager::Settings.isShowWorring);
    ImGui::TableNextColumn();
    ImGui::M_SwitchButton("手持", &ConfigManager::Settings.isShowWeaponID);
    ImGui::TableNextColumn();
    ImGui::M_SwitchButton("人机", &ConfigManager::Settings.isShowBot);
    ImGui::TableNextColumn();
    ImGui::M_SwitchButton("雷达", &ConfigManager::Settings.isRadar);
    ImGui::TableNextColumn();
    ImGui::TableNextColumn();
    ImGui::SliderFloat("雷达位置x", &ConfigManager::Settings.RadarX, 0, screen_x);
    ImGui::TableNextColumn();
    ImGui::SliderFloat("雷达位置y", &ConfigManager::Settings.RadarY, 0, screen_y);
    ImGui::TableNextColumn();
    ImGui::SliderFloat("雷达显示距离", &ConfigManager::Settings.RadarSize, 50, 300);

    ImGui::EndTable();
  }
}

void AimbotTabContents() {
  ImGui::Checkbox("启用触摸自瞄", &ConfigManager::Settings.isAimbot);
  ImGui::SameLine();
  ImGui::Checkbox("烟雾后不瞄", &ConfigManager::Settings.isNotAimSmoke);

  ImGui::Checkbox("持续锁定", &ConfigManager::Settings.isContinued);
  ImGui::SameLine();
  ImGui::Checkbox("击倒不瞄", &ConfigManager::Settings.isKnockdown);
  ImGui::SameLine();
  ImGui::Checkbox("人机不描", &ConfigManager::Settings.isNotAimRobot);

  ImGui::Checkbox("显示范围", &ConfigManager::Settings.isRange);
  ImGui::SameLine();
  ImGui::Checkbox("显示触摸区域", &ConfigManager::Settings.isTouch);
  ImGui::SameLine();
  ImGui::Checkbox("显示开火位置", &ConfigManager::Settings.isFTouch);

  ImGui::Combo("触发方式", &ConfigManager::Settings.LockMode, "开火自瞄\0开镜自瞄\0开火 or 开镜\00开火 and 开镜\0");
  ImGui::Combo("瞄准部位", &ConfigManager::Settings.LockPos, "自动选择\0玩家头部\0玩家胸部\0玩家腰部\0");

  if (ImGui::CollapsingHeader("自瞄调整")) {
    ImGui::SliderFloat("自瞄范围", &ConfigManager::Settings.Range, 0, 600);
    ImGui::SliderFloat("腰射自瞄玩家距离", &ConfigManager::Settings.NotaimDistance, 0, 500);
    ImGui::SliderFloat("腰射自瞄人机距离", &ConfigManager::Settings.NotaimBotDistance, 0, 500);
    ImGui::SliderFloat("开镜自瞄玩家距离", &ConfigManager::Settings.aimDistance, 0, 500);
    ImGui::SliderFloat("开镜自瞄人机距离", &ConfigManager::Settings.aimBotDistance, 0, 500);
    ImGui::SliderFloat("预判系数", &ConfigManager::Settings.Predict, 0.001, 0.3);
    if (ImGui::SliderFloat("触摸左右滑动速度", &ConfigManager::Settings.Speed_X, 0.01f, 0.20f, "%.2f")) {
      touch_information.floatswitch[1] = ConfigManager::Settings.Speed_X;
    }
    if (ImGui::SliderFloat("触摸上下滑动速度", &ConfigManager::Settings.Speed_Y, 0.01f, 0.20f, "%.2f")) {
      touch_information.floatswitch[2] = ConfigManager::Settings.Speed_Y;
    }
    if (ImGui::SliderFloat("触摸左右滑动精度", &ConfigManager::Settings.accuracy_X, 0.01f, 0.50f, "%.2f")) {
      touch_information.Accuracy_X = ConfigManager::Settings.accuracy_X;
    }
    if (ImGui::SliderFloat("触摸上下滑动精度", &ConfigManager::Settings.accuracy_Y, 0.01f, 0.50f, "%.2f")) {
      touch_information.Accuracy_Y = ConfigManager::Settings.accuracy_Y;
    }
    if (ImGui::SliderFloat("触摸屏幕滑动速度", &ConfigManager::Settings.Speed, 0.01f, 1.00f, "%.2f")) {
      touch_information.floatswitch[3] = ConfigManager::Settings.Speed;
      touch_information.TouchOrientationControl = true;
    }
  }

  if (ImGui::CollapsingHeader("压枪调整")) {
    ImGui::SliderFloat("压枪倍数", &ConfigManager::Settings.Down, 0.1, 10);
    ImGui::SliderFloat("蹲下压枪", &ConfigManager::Settings.Squat_Down, 0.1, 1);
    ImGui::SliderFloat("趴下压枪", &ConfigManager::Settings.Lying_Down, 0.1, 1);
    ImGui::SliderFloat("手持枪械压枪", &WeaponMap[MainUpData.SelfWeaponType], 0.1, 2);
  }
  if (ImGui::CollapsingHeader("触摸调整")) {
    if (ImGui::SliderFloat("触摸位置左右", &ConfigManager::Settings.TouchPosy, 0.f, 1.f, "%.2f")) {
      touch_information.TouchPoints.y = ConfigManager::Settings.TouchPosy;
      touch_information.TouchOrientationControl = true;
    }
    if (ImGui::SliderFloat("触摸位置上下", &ConfigManager::Settings.TouchPosx, 0.f, 1.f, "%.2f")) {
      touch_information.TouchPoints.x = ConfigManager::Settings.TouchPosx;
      touch_information.TouchOrientationControl = true;
    }
    if (ImGui::SliderFloat("触摸区域大小", &ConfigManager::Settings.TouchRange, 0.03f, 0.08f, "%.2f")) {
      touch_information.TouchRadius = ConfigManager::Settings.TouchRange;
      touch_information.TouchOrientationControl = true;
    }
    if (ImGui::SliderFloat("触摸帧数设置", &ConfigManager::Settings.TouchFPS, 500.f, 2000.f, "%.2f")) {
      touch_information.floatswitch[0] = ConfigManager::Settings.TouchFPS;
      touch_information.TouchOrientationControl = true;
    }
  }
}

void InfoTabContents() {
  ImGui::Checkbox("物资总开关", &ConfigManager::Settings.isShowarticle);

  ImGui::Checkbox("投掷", &ConfigManager::Settings.ShowItemTypes[1]);
  ImGui::SameLine();
  ImGui::Checkbox("载具", &ConfigManager::Settings.ShowItemTypes[2]);
  ImGui::SameLine();
  ImGui::Checkbox("防具", &ConfigManager::Settings.ShowItemTypes[3]);
  ImGui::SameLine();
  ImGui::Checkbox("道具", &ConfigManager::Settings.ShowItemTypes[4]);

  ImGui::Checkbox("盒子", &ConfigManager::Settings.ShowItemTypes[5]);
  ImGui::SameLine();
  ImGui::Checkbox("药品", &ConfigManager::Settings.ShowItemTypes[6]);
  ImGui::SameLine();
  ImGui::Checkbox("子弹", &ConfigManager::Settings.ShowItemTypes[7]);
  ImGui::SameLine();
  ImGui::Checkbox("762", &ConfigManager::Settings.ShowItemTypes[8]);

  ImGui::Checkbox("556", &ConfigManager::Settings.ShowItemTypes[9]);
  ImGui::SameLine();
  ImGui::Checkbox("冲锋", &ConfigManager::Settings.ShowItemTypes[10]);
  ImGui::SameLine();
  ImGui::Checkbox("霰弹", &ConfigManager::Settings.ShowItemTypes[11]);
  ImGui::SameLine();
  ImGui::Checkbox("狙击", &ConfigManager::Settings.ShowItemTypes[12]);

  ImGui::Checkbox("其他", &ConfigManager::Settings.ShowItemTypes[13]);
  ImGui::SameLine();
  ImGui::Checkbox("步配", &ConfigManager::Settings.ShowItemTypes[14]);
  ImGui::SameLine();
  ImGui::Checkbox("倍镜", &ConfigManager::Settings.ShowItemTypes[15]);
  ImGui::SameLine();
  ImGui::Checkbox("地铁", &ConfigManager::Settings.ShowItemTypes[16]);

  if (ImGui::Button("全部显示")) {
    for (auto &pair : ConfigManager::Settings.ShowItemTypes) {
      pair.second = true;
    }
  }
  ImGui::SameLine();
  if (ImGui::Button("全部隐藏")) {
    for (auto &pair : ConfigManager::Settings.ShowItemTypes) {
      pair.second = false;
    }
  }

  unsigned long map_size = GameTools::MaterialsIntoVector.size();
  for (int i = 0; i < map_size; i++) {
    if (ImGui::BeginTable("split", 2, ImGuiTableFlags_Borders | ImGuiTableFlags_Resizable | ImGuiTableFlags_Reorderable | ImGuiTableFlags_Hideable)) {
      ImGui::TableSetupColumn(GameTools::MaterialsIntoVector[i].name.c_str(), ImGuiTableColumnFlags_WidthStretch);
      ImGui::TableSetupColumn("", ImGuiTableColumnFlags_WidthStretch);
      ImGui::TableHeadersRow();
      ImGui::TableNextColumn();
      if (ImGui::Button("全部显示")) {
        for (auto map : GameTools::MaterialsIntoVector[i].MapBOOL) {
          GameTools::MaterialsIntoVector[i].MapBOOL[map.first.c_str()] = true;
        }
      }
      ImGui::TableNextColumn();
      if (ImGui::Button("全部隐藏")) {
        for (auto map : GameTools::MaterialsIntoVector[i].MapBOOL) {
          GameTools::MaterialsIntoVector[i].MapBOOL[map.first.c_str()] = false;
        }
      }
      for (auto map : GameTools::MaterialsIntoVector[i].MapBOOL) {
        ImGui::TableNextColumn();
        ImGui::Checkbox(GameTools::MaterialsIntoVector[i].Map[map.first.c_str()].c_str(), &GameTools::MaterialsIntoVector[i].MapBOOL[map.first.c_str()]);
      }
      ImGui::EndTable();
    }
  }
}

extern uintptr_t CameraPOV_address;
void SetTabContents(bool *main_thread_flag, int &style_idx) {
  if (ImGui::Checkbox("过录制", &::permeate_record)) {
    ::permeate_record_ini = true;
  }
  ImGui::SameLine();
  ImGui::Checkbox("DeBug", &ConfigManager::Settings.DEBUG);
  if (ConfigManager::Settings.DEBUG) {
    ImGui::Text("自身数据");
    ImGui::Text("libue4: 0x%ld", libUe4_address);
    ImGui::Text("MyState: %d", MainUpData.SelfState);
    ImGui::Text("CameraPos: %.1f %.1f %.1f", MainUpData.SelfViewInfo.Location.x, MainUpData.SelfViewInfo.Location.y, MainUpData.SelfViewInfo.Location.z);
    ImGui::Text("POV: %p", CameraPOV_address);
    ImGui::Text("NowRot: %.1f %.1f", MainUpData.SelfControlRotation.Yaw, MainUpData.SelfControlRotation.Pitch);
    ImGui::Text("Fov: %.1f", MainUpData.SelfViewInfo.FOV);
    ImGui::Text("WeaponId: %d", MainUpData.SelfWeaponType);
    if (ImGui::Button("输出准心类名", {-1, 80})) {
      float minCount = 9999;
      string minAt = "";
      for (const auto &className : MainIntoList) {
        if (className.Type == -1) {
          float Screen_Distance = sqrt(pow(screen_x / 2 - className.ScreenPos.x, 2) + pow(screen_y / 2 - className.ScreenPos.y, 2));
          if (Screen_Distance < minCount) {
            minAt = className.Name;
            minCount = Screen_Distance;
          }
        }
      }
      cout << endl
           << minAt << endl;
    }
    if (ImGui::Button("开启灵动岛", {-1, 80})) {
      dynamic_Island_XCJ::SetIslandFlage(true);
    }
    if (ImGui::Button("关闭灵动岛", {-1, 80})) {
      dynamic_Island_XCJ::SetIslandFlage(false);
    }

    if (ImGui::Button("开启悬浮窗", {-1, 80})) {
      dynamic_Island_XCJ::SetWindowFlage(true);
    }
    if (ImGui::Button("关闭悬浮窗", {-1, 80})) {
      dynamic_Island_XCJ::SetWindowFlage(false);
    }
  }

  ImGui::Text("渲染模式 : %s, GM版本 : %s", graphics->RenderName, IMGUI_VERSION); // Display some text (you can use a format strings too)
  if (ImGui::Combo("##主题", &style_idx, "白色主题\0蓝色主题\0紫色主题\0")) {
    switch (style_idx) {
    case 0:
      ImGui::StyleColorsLight();
      break;
    case 1:
      ImGui::StyleColorsDark();
      break;
    case 2:
      ImGui::StyleColorsClassic();
      break;
    }
  }

  ImGui::Text("##FPS调整");
  if (ImGui::RadioButton("60", &ConfigManager::Settings.RunFPS, 60.0f)) {
    MainThreadTimer.SetFps(ConfigManager::Settings.RunFPS);
    aimtiem.SetFps(ConfigManager::Settings.RunFPS);
  }
  ImGui::SameLine();
  if (ImGui::RadioButton("90", &ConfigManager::Settings.RunFPS, 90.0f)) {
    MainThreadTimer.SetFps(ConfigManager::Settings.RunFPS);
    aimtiem.SetFps(ConfigManager::Settings.RunFPS);
  }
  ImGui::SameLine();
  if (ImGui::RadioButton("120", &ConfigManager::Settings.RunFPS, 120.0f)) {
    MainThreadTimer.SetFps(ConfigManager::Settings.RunFPS);
    aimtiem.SetFps(ConfigManager::Settings.RunFPS);
  }
  ImGui::SameLine();
  if (ImGui::RadioButton("144", &ConfigManager::Settings.RunFPS, 144.0f)) {
    MainThreadTimer.SetFps(ConfigManager::Settings.RunFPS);
    aimtiem.SetFps(ConfigManager::Settings.RunFPS);
  }

  ImGui::SliderFloat("灵动岛大小X", &ConfigManager::Settings.IslandSize.x, 0, 2400);
  ImGui::SliderFloat("灵动岛大小Y", &ConfigManager::Settings.IslandSize.y, 0, 2400);
  ImGui::SliderFloat("玩家骨骼显示距离", &ConfigManager::Settings.showBoneDistance, 0, 500);
  ImGui::SliderFloat("人机骨骼显示距离", &ConfigManager::Settings.showBotBoneDistance, 0, 500);
  ImGui::Combo("预警显示方式", &ConfigManager::Settings.articleMode, "自瞄圈圈周围\0屏幕边缘\0");

  if (ImGui::CollapsingHeader("绘制粗细设置")) {
    ImGui::SliderFloat("玩家射线", &ConfigManager::Settings.LineSize, 0.1, 10.0f);
    ImGui::SliderFloat("BOT射线", &ConfigManager::Settings.BotLineSize, 0.1, 10.0);
    ImGui::SliderFloat("玩家骨骼", &ConfigManager::Settings.BoneSize, 0.1, 10.0);
    ImGui::SliderFloat("BOT骨骼", &ConfigManager::Settings.BotBoneSize, 0.1, 10.0f);
  }

  if (ImGui::CollapsingHeader("绘制颜色设置")) {
    ImGui::ColorEdit4("玩家方框背景", (float *)&ConfigManager::Settings.BoxblackColor, ImGuiColorEditFlags_NoInputs);
    ImGui::SameLine();
    ImGui::ColorEdit4("BOT方框背景", (float *)&ConfigManager::Settings.BotBoxblackColor, ImGuiColorEditFlags_NoInputs);

    ImGui::ColorEdit4("玩家射线", (float *)&ConfigManager::Settings.LineColor, ImGuiColorEditFlags_NoInputs);
    ImGui::SameLine();
    ImGui::ColorEdit4("BOT射线", (float *)&ConfigManager::Settings.BotLineColor, ImGuiColorEditFlags_NoInputs);

    ImGui::ColorEdit4("玩家骨骼", (float *)&ConfigManager::Settings.BoneColor, ImGuiColorEditFlags_NoInputs);
    ImGui::SameLine();
    ImGui::ColorEdit4("BOT骨骼", (float *)&ConfigManager::Settings.BotBoneColor, ImGuiColorEditFlags_NoInputs);
  }

  if (ImGui::Button("退出", {-1, 75})) {
    // 退出前保存配置
    ConfigManager::SaveConfigManager();
    *main_thread_flag = false;
  }
}

void Layout_tick_UI_Tips(bool *main_thread_flag, const std::string &tips) {
  ImGui::Begin("错误", main_thread_flag); // Create a window called "Hello, world!" and append into it.
  {

    ImGui::Text("%s", tips.c_str());

    g_window = ImGui::GetCurrentWindow();
    ImGui::End();
  }
}

void FixTriangle(float &XPos, float &YPos, int screenDist) {
  if (XPos > screen_x) {
    XPos = screen_x;
    XPos -= screenDist + 35;
  }

  if (XPos < 0) {
    XPos = 0;
    XPos += screenDist + 35;
  }

  if (YPos > screen_y) {
    YPos = screen_y;
    YPos -= screenDist;
  }
  if (YPos < 0) {
    YPos = 0;
    YPos += screenDist;
  }
}

void DrawESP(ImDrawList *Draw) {
  int PlayerCount = 0, BotCount = 0;
  XCJDrawList::thisDrawList = Draw;

  string logostr = "FPS：" + to_string((int)ImGui::GetIO().Framerate);
  Draw->AddRectFilled({150 - 20, 30 - 10}, {150 + 150, 30 + 50}, ImColor(0, 0, 0, 100), 45);
  XCJDrawList::drawTextY(logostr.c_str(), {150, 30}, ImColor(255, 255, 255), true, 38);

  if (ConfigManager::Settings.isRange) {
    if (Aim_At != -1 && ConfigManager::Settings.isAimbot) {
      Draw->AddLine({static_cast<float>(screen_x / 2), static_cast<float>(screen_y / 2)}, {MainPlayerList[Aim_At].player_bone.Head.ScreenPos.x, MainPlayerList[Aim_At].player_bone.Head.ScreenPos.y}, ImColor(255, 0, 0), {2.5f});
    }
    Draw->AddCircle({static_cast<float>(screen_x / 2), static_cast<float>(screen_y / 2)}, ConfigManager::Settings.Range, ImColor(255, 0, 0), 0, 2.5f);
  }

  if (ConfigManager::Settings.isRadar) {
    Draw->AddCircleFilled({ConfigManager::Settings.RadarX, ConfigManager::Settings.RadarY}, {150}, ImColor(0, 0, 0, 55));
    Draw->AddCircle({ConfigManager::Settings.RadarX, ConfigManager::Settings.RadarY}, {150}, ImColor(ImVec4(255 / 255.f, 255 / 255.f, 258 / 255.f, 1.0f)));
    Draw->AddLine({ConfigManager::Settings.RadarX + 150, ConfigManager::Settings.RadarY}, {ConfigManager::Settings.RadarX - 150, ConfigManager::Settings.RadarY}, ImColor(ImVec4(255 / 255.f, 255 / 255.f, 258 / 255.f, 0.5f)), 2);
    Draw->AddLine({ConfigManager::Settings.RadarX, ConfigManager::Settings.RadarY + 150}, {ConfigManager::Settings.RadarX, ConfigManager::Settings.RadarY - 150}, ImColor(ImVec4(255 / 255.f, 255 / 255.f, 258 / 255.f, 0.5f)), 2);
  }

  if (ConfigManager::Settings.isTouch) {
    float CoordinatesW = resolution_information.ScreenWidth * touch_information.TouchRadius;
    float CoordinatesX = resolution_information.ScreenWidth * touch_information.TouchPoints.y;
    float CoordinatesY = resolution_information.ScreenHeiht * (1 - touch_information.TouchPoints.x);
    string Text = "";
    Text += "触摸控制区域";
    ImVec2 TextSize = ImGui::CalcTextSize(Text.c_str(), 0, 30);
    Draw->AddText(NULL, 30, {CoordinatesX - (TextSize.x / 2), CoordinatesY - (TextSize.y / 2)}, ImColor(255, 255, 255, 255), Text.c_str());
    Draw->AddRectFilled({CoordinatesX - CoordinatesW, CoordinatesY - CoordinatesW}, {CoordinatesX + CoordinatesW, CoordinatesY + CoordinatesW}, ImColor(200, 0, 0, 100));
  }

  if (GameTools::IsStartRead)
    UpPlayerData();
  else
    MainPlayerList.clear();

  for (const auto &NowPlayerList : MainPlayerList) {
    if (ConfigManager::Settings.isRadar) {
      Vec2 Radar = GameTools::rotateCoord(MainUpData.SelfControlRotation.Yaw - 90, (MainUpData.SelfCoordinate.x - NowPlayerList.Pos.x) / (ConfigManager::Settings.RadarSize), (MainUpData.SelfCoordinate.y - NowPlayerList.Pos.y) / (ConfigManager::Settings.RadarSize));

      if (GameTools::GetDistance({ConfigManager::Settings.RadarX, ConfigManager::Settings.RadarY}, {Radar.x + ConfigManager::Settings.RadarX, Radar.y + ConfigManager::Settings.RadarY}) <= 135.0) {
        if (NowPlayerList.IsBot) {
          Draw->AddCircle({ConfigManager::Settings.RadarX + Radar.x, ConfigManager::Settings.RadarY + Radar.y}, {15}, ImColor(0, 0, 0), 0, 1.5);
          Draw->AddCircleFilled({ConfigManager::Settings.RadarX + Radar.x, ConfigManager::Settings.RadarY + Radar.y}, {15}, ImColor(255, 255, 255));
        } else {
          transparent = 255.f / 255.f;
          ;
          Draw->AddCircle({ConfigManager::Settings.RadarX + Radar.x, ConfigManager::Settings.RadarY + Radar.y}, {15}, ImColor(0, 0, 0), 0, 1.5);
          Draw->AddCircleFilled({ConfigManager::Settings.RadarX + Radar.x, ConfigManager::Settings.RadarY + Radar.y}, {15}, ImColor(arr[NowPlayerList.TeamID % length]));
        }
      }
    }

    transparent = 215 / 255.f;
    if (NowPlayerList.player_bone.Chest.ScreenPos.x < screen_x && NowPlayerList.player_bone.Chest.ScreenPos.x > 0 &&
        NowPlayerList.player_bone.Chest.ScreenPos.y < screen_y && NowPlayerList.player_bone.Chest.ScreenPos.y > 0) {

      float left = NowPlayerList.player_bone.Head.ScreenPos.x - NowPlayerList.width * 0.6;
      float right = NowPlayerList.player_bone.Head.ScreenPos.x + NowPlayerList.width * 0.6;
      float top = NowPlayerList.player_bone.Pelvis.ScreenPos.y - (NowPlayerList.player_bone.Pelvis.ScreenPos.y - NowPlayerList.player_bone.Head.ScreenPos.y) - NowPlayerList.width / 5;
      float bottom = NowPlayerList.player_bone.Head.ScreenPos.y + NowPlayerList.width * 2;

      if (ConfigManager::Settings.isShowBox) {
        // 开火 273 1296 4348 5392 65552
        if (NowPlayerList.Health > 0) {
          if (NowPlayerList.IsBot) {
            Draw->AddRect({left, top}, {right, bottom}, ImColor(255, 255, 255), {0}, 0, {1.5});
            Draw->AddRectFilled({left, top}, {right, bottom}, ImColor(255, 255, 255, 35));
          } else {
            if (NowPlayerList.State == 273 || NowPlayerList.State == 1296 || NowPlayerList.State == 4348 || NowPlayerList.State == 5392 || NowPlayerList.State == 65552) {
              Draw->AddRect({left, top}, {right, bottom}, ImColor(255, 0, 0), {0}, 0, {1.5});
              Draw->AddRectFilled({left, top}, {right, bottom}, ImColor(255, 0, 0, 35));
            } else {
              Draw->AddRect({left, top}, {right, bottom}, ImColor(0, 235, 0), {0}, 0, {1.5});
              Draw->AddRectFilled({left, top}, {right, bottom}, ImColor(0, 255, 0, 35));
            }
          }
        } else {
          Draw->AddRect({left, top}, {right, bottom}, ImColor(0, 0, 0), {0}, 0, {1.5});
          Draw->AddRectFilled({left, top}, {right, bottom}, ImColor(0, 0, 0, 35));
        }
      }

      if (ConfigManager::Settings.isShowLine) {
        if (NowPlayerList.IsBot) {
          Draw->AddLine({static_cast<float>(screen_x / 2), 120}, {NowPlayerList.player_bone.Head.ScreenPos.x, NowPlayerList.player_bone.Head.ScreenPos.y}, ConfigManager::Settings.BotLineColor, {ConfigManager::Settings.BotLineSize});
        } else {
          Draw->AddLine({static_cast<float>(screen_x / 2), 120}, {NowPlayerList.player_bone.Head.ScreenPos.x, NowPlayerList.player_bone.Head.ScreenPos.y}, ConfigManager::Settings.LineColor, {ConfigManager::Settings.LineSize});
        }
      }

      if (ConfigManager::Settings.isShowHealth) {
        ImVec4 HPColor = NowPlayerList.Health < 80 ? NowPlayerList.Health < 60 ? NowPlayerList.Health < 30 ? ImVec4{0.5f * 255, 0, 0, 127} : ImVec4{255, 0, 0, 127} : ImVec4{255, 255, 0, 127} : ImVec4{255, 255, 255, 127};
        Draw->AddRect({NowPlayerList.player_bone.Head.ScreenPos.x - 50, top - 28}, {NowPlayerList.player_bone.Head.ScreenPos.x + 50, top - 18}, ImColor(0, 0, 0, 240));
        Draw->AddRectFilled({NowPlayerList.player_bone.Head.ScreenPos.x - 50, top - 27.5f}, {NowPlayerList.player_bone.Head.ScreenPos.x - 50 + NowPlayerList.Health, top - 17.5f}, ImColor(HPColor));
      }

      if (ConfigManager::Settings.isShowName) {
        string sname;
        if (!NowPlayerList.IsBot) {
          sname += to_string(NowPlayerList.TeamID);
          sname += ". ";
          sname += NowPlayerList.PlayerName;
          auto textSizes = ImGui::CalcTextSize(sname.c_str(), 0, 23);
          Draw->AddRectFilled({NowPlayerList.player_bone.Head.ScreenPos.x - (textSizes.x / 2) - 5, top - 65}, {NowPlayerList.player_bone.Head.ScreenPos.x + (textSizes.x / 2) + 5, top - 30}, ImColor(35, 35, 35, 75));
          Draw->AddText(NULL, 32, {NowPlayerList.player_bone.Head.ScreenPos.x - (textSizes.x / 2), top - 62}, ImColor(245, 245, 245), sname.c_str());
        } else {
          sname += "RoBot";
          auto textSizes = ImGui::CalcTextSize(sname.c_str(), 0, 23);
          Draw->AddText(NULL, 32, {NowPlayerList.player_bone.Head.ScreenPos.x - (textSizes.x / 2), top - 62}, ImColor(245, 245, 245), sname.c_str());
        }
      }

      if (ConfigManager::Settings.isShowDistance) {
        string str = to_string((int)NowPlayerList.Distance);
        str += " m";
        auto textSizes = ImGui::CalcTextSize(str.c_str(), 0, 35);
        Draw->AddText(NULL, 25, {NowPlayerList.player_bone.Head.ScreenPos.x - (textSizes.x / 2), bottom + 15}, ImColor(255, 125, 80), str.c_str());
      }

      /*if (ConfigManager::Settings.isShowWeaponID) {
          ImTextureID DrawWeapons = Tool->GetWeaponsphoto(NowPlayerList.WeaponID);
          Draw->AddImage(DrawWeapons, {NowPlayerList.player_bone.Head.ScreenPos.x + 50, top - 100}, {NowPlayerList.player_bone.Head.ScreenPos.x - 50, top - 60});
      }*/

      if (ConfigManager::Settings.isShowBone) {
        bool IsDraw = true;
        if (NowPlayerList.IsBot) {
          if (NowPlayerList.Distance > ConfigManager::Settings.showBotBoneDistance) {
            IsDraw = false;
          }
          XCJDrawList::BoneDrawColor = ConfigManager::Settings.BotBoneColor;
        } else {
          if (NowPlayerList.Distance > ConfigManager::Settings.showBoneDistance) {
            IsDraw = false;
          }
          XCJDrawList::BoneDrawColor = ConfigManager::Settings.BoneColor;
        }
        if (IsDraw) {
          if (NowPlayerList.player_bone.Head.CanSee) {
            Draw->AddCircle({NowPlayerList.player_bone.Head.ScreenPos.x, NowPlayerList.player_bone.Head.ScreenPos.y}, NowPlayerList.width / 14, XCJDrawList::BoneDrawColor, 0, ConfigManager::Settings.BoneSize);
          } else {
            Draw->AddCircle({NowPlayerList.player_bone.Head.ScreenPos.x, NowPlayerList.player_bone.Head.ScreenPos.y}, NowPlayerList.width / 14, ImColor(0, 255, 0), 0, ConfigManager::Settings.BotBoneSize);
          }
          XCJDrawList::drawBone({NowPlayerList.player_bone.Head.ScreenPos.x, NowPlayerList.player_bone.Head.ScreenPos.y}, {NowPlayerList.player_bone.Chest.ScreenPos.x, NowPlayerList.player_bone.Chest.ScreenPos.y}, NowPlayerList.player_bone.Chest.CanSee);
          XCJDrawList::drawBone({NowPlayerList.player_bone.Chest.ScreenPos.x, NowPlayerList.player_bone.Chest.ScreenPos.y}, {NowPlayerList.player_bone.Pelvis.ScreenPos.x, NowPlayerList.player_bone.Pelvis.ScreenPos.y}, NowPlayerList.player_bone.Pelvis.CanSee);
          XCJDrawList::drawBone({NowPlayerList.player_bone.Chest.ScreenPos.x, NowPlayerList.player_bone.Chest.ScreenPos.y}, {NowPlayerList.player_bone.Left_Shoulder.ScreenPos.x, NowPlayerList.player_bone.Left_Shoulder.ScreenPos.y}, NowPlayerList.player_bone.Left_Shoulder.CanSee);
          XCJDrawList::drawBone({NowPlayerList.player_bone.Chest.ScreenPos.x, NowPlayerList.player_bone.Chest.ScreenPos.y}, {NowPlayerList.player_bone.Right_Shoulder.ScreenPos.x, NowPlayerList.player_bone.Right_Shoulder.ScreenPos.y}, NowPlayerList.player_bone.Right_Shoulder.CanSee);
          XCJDrawList::drawBone({NowPlayerList.player_bone.Left_Shoulder.ScreenPos.x, NowPlayerList.player_bone.Left_Shoulder.ScreenPos.y}, {NowPlayerList.player_bone.Left_Elbow.ScreenPos.x, NowPlayerList.player_bone.Left_Elbow.ScreenPos.y}, NowPlayerList.player_bone.Left_Elbow.CanSee);
          XCJDrawList::drawBone({NowPlayerList.player_bone.Right_Shoulder.ScreenPos.x, NowPlayerList.player_bone.Right_Shoulder.ScreenPos.y}, {NowPlayerList.player_bone.Right_Elbow.ScreenPos.x, NowPlayerList.player_bone.Right_Elbow.ScreenPos.y}, NowPlayerList.player_bone.Right_Elbow.CanSee);
          XCJDrawList::drawBone({NowPlayerList.player_bone.Left_Elbow.ScreenPos.x, NowPlayerList.player_bone.Left_Elbow.ScreenPos.y}, {NowPlayerList.player_bone.Left_Wrist.ScreenPos.x, NowPlayerList.player_bone.Left_Wrist.ScreenPos.y}, NowPlayerList.player_bone.Left_Wrist.CanSee);
          XCJDrawList::drawBone({NowPlayerList.player_bone.Right_Elbow.ScreenPos.x, NowPlayerList.player_bone.Right_Elbow.ScreenPos.y}, {NowPlayerList.player_bone.Right_Wrist.ScreenPos.x, NowPlayerList.player_bone.Right_Wrist.ScreenPos.y}, NowPlayerList.player_bone.Right_Wrist.CanSee);
          XCJDrawList::drawBone({NowPlayerList.player_bone.Pelvis.ScreenPos.x, NowPlayerList.player_bone.Pelvis.ScreenPos.y}, {NowPlayerList.player_bone.Left_Thigh.ScreenPos.x, NowPlayerList.player_bone.Left_Thigh.ScreenPos.y}, NowPlayerList.player_bone.Left_Thigh.CanSee);
          XCJDrawList::drawBone({NowPlayerList.player_bone.Pelvis.ScreenPos.x, NowPlayerList.player_bone.Pelvis.ScreenPos.y}, {NowPlayerList.player_bone.Right_Thigh.ScreenPos.x, NowPlayerList.player_bone.Right_Thigh.ScreenPos.y}, NowPlayerList.player_bone.Right_Thigh.CanSee);
          XCJDrawList::drawBone({NowPlayerList.player_bone.Left_Thigh.ScreenPos.x, NowPlayerList.player_bone.Left_Thigh.ScreenPos.y}, {NowPlayerList.player_bone.Left_Knee.ScreenPos.x, NowPlayerList.player_bone.Left_Knee.ScreenPos.y}, NowPlayerList.player_bone.Left_Knee.CanSee);
          XCJDrawList::drawBone({NowPlayerList.player_bone.Right_Thigh.ScreenPos.x, NowPlayerList.player_bone.Right_Thigh.ScreenPos.y}, {NowPlayerList.player_bone.Right_Knee.ScreenPos.x, NowPlayerList.player_bone.Right_Knee.ScreenPos.y}, NowPlayerList.player_bone.Right_Knee.CanSee);
          XCJDrawList::drawBone({NowPlayerList.player_bone.Left_Knee.ScreenPos.x, NowPlayerList.player_bone.Left_Knee.ScreenPos.y}, {NowPlayerList.player_bone.Left_Ankle.ScreenPos.x, NowPlayerList.player_bone.Left_Ankle.ScreenPos.y}, NowPlayerList.player_bone.Left_Ankle.CanSee);
          XCJDrawList::drawBone({NowPlayerList.player_bone.Right_Knee.ScreenPos.x, NowPlayerList.player_bone.Right_Knee.ScreenPos.y}, {NowPlayerList.player_bone.Right_Ankle.ScreenPos.x, NowPlayerList.player_bone.Right_Ankle.ScreenPos.y}, NowPlayerList.player_bone.Right_Ankle.CanSee);
        }
      }
    } else {
      if (ConfigManager::Settings.isShowWorring) {
        float Angle = MainUpData.SelfControlRotation.Yaw - 90 - GameTools::toRotator(MainUpData.SelfViewInfo.Location, NowPlayerList.Pos).Yaw - 180;
        Vec2 BeiAngle = GameTools::rotateCoord(Angle, screen_x, 0);

        Vec2 BeiScreen;
        BeiScreen = GameTools::rotateCoord(Angle, 0, 0);
        BeiScreen.x += screen_x / 2 + BeiAngle.x;
        BeiScreen.y += screen_y / 2 + BeiAngle.y;
        FixTriangle(BeiScreen.x, BeiScreen.y, 45);

        if (!NowPlayerList.IsBot) {
          // 绘制填充圆形
          Draw->AddCircleFilled(ImVec2(BeiScreen.x, BeiScreen.y), 35, ImColor(arr[NowPlayerList.TeamID % length]));
          Draw->AddCircle(ImVec2(BeiScreen.x, BeiScreen.y), 25, ImColor(0, 0, 0, 255), 0.0f, 2);
        } else {
          // 绘制填充圆形
          Draw->AddCircleFilled(ImVec2(BeiScreen.x, BeiScreen.y), 35, ImColor(255, 255, 255, 255));
          Draw->AddCircle(ImVec2(BeiScreen.x, BeiScreen.y), 25, ImColor(0, 0, 0, 255), 0.0f, 2);
        }
      }
    }

    if (NowPlayerList.IsBot) {
      BotCount++;
    } else {
      PlayerCount++;
    }
  }

  // 绘制物品
  if (ConfigManager::Settings.isShowarticle) {
    for (const auto &item : MainIntoList) {
      // 检查物品是否在屏幕范围内
      if (item.ScreenPos.x > 0 && item.ScreenPos.x < screen_x &&
          item.ScreenPos.y > 0 && item.ScreenPos.y < screen_y) {

        // 绘制物品名称+距离（一行显示）
        string itemText = item.Name + "_" + to_string((int)item.Distance) + "m";
        auto textSize = ImGui::CalcTextSize(itemText.c_str());

        // 绘制物品文本（居中显示）
        Draw->AddText(
            NULL,
            18,
            {item.ScreenPos.x - textSize.x / 2, item.ScreenPos.y - textSize.y / 2},
            item.color,
            itemText.c_str());
      }
    }
  }

  if (MainPlayerList.size() == 0 && MainIntoList.size() == 0 && GameTools::MainVehicleList.size() == 0) {
    dynamic_Island_XCJ::SetIslandFlage(false);
    dynamic_Island_XCJ::SetText("");
  } else {
    dynamic_Island_XCJ::SetIslandFlage(true);
    if (BotCount + PlayerCount == 0) {
      dynamic_Island_XCJ::SetText("Safe");
    } else {
      dynamic_Island_XCJ::SetText("Player: " + to_string(PlayerCount) + " Bot: " + to_string(BotCount));
    }
  }
}