#ifndef NATIVESURFACE_DRAW_H
#define NATIVESURFACE_DRAW_H

#include <stdio.h>
#include <stdlib.h>

#include "imgui.h"
#include "imgui_internal.h"
#include "ANativeWindowCreator.h"

#include "AndroidImgui.h"
#include "TouchHelperA.h"//触摸
#include "my_imgui.h"     //字体
#include "timer.h"

// 窗口
extern ANativeWindow *window;
// 屏幕信息
extern android::ANativeWindowCreator::DisplayInfo displayInfo;
// 窗口信息
extern ImGuiWindow *g_window;
// 绝对屏幕X _ Y
extern int abs_ScreenX, abs_ScreenY;
// 当前屏幕X _ Y
extern int screen_x, screen_y;
// 窗口渲染大小 X _ Y
extern int native_window_screen_x, native_window_screen_y;
// 自定义屏幕X _ Y
extern int screen_x_set, screen_y_set;
// 是否设置屏幕信息
extern bool is_set_screen;
// 悬浮窗图片
extern BaseTexData *logo_image;
// 线程计时器
extern Timer MainThreadTimer;
// 通知
extern DynamicIsland* notificationManager;
// 触摸类
extern RESOLUTION_INFORMATION resolution_information;
extern TOUCH_INFORMATION touch_information;

// 字体
extern std::unique_ptr<AndroidImgui>  graphics;

// 上次UI位置
struct Last_ImRect {
    float Pos_x;
    float Pos_y;
    float Size_x;
    float Size_y;
};
extern struct Last_ImRect LastCoordinate;
//是否过录制
extern bool animating;
extern bool BallSwitch;
extern bool NowBallSwitch;
extern bool permeate_record;
extern bool permeate_record_ini;


// 获取屏幕信息
extern void screen_config();
// 绘制开始
extern void drawBegin();
// 布局UI
extern void HideVolumeKeys(bool *is_finish);
// 监听音量键
extern void Layout_tick_UI(bool *main_thread_flag);
// 未刷入驱动提示
extern void Layout_tick_UI_Tips(bool *main_thread_flag, const std::string &tips);
extern void AimbotTabContents();
extern void InfoTabContents();
extern void SetTabContents(bool* main_thread_flag, int& style_idx);
extern void DrawTabContents(int& GameSetToUI);
extern void StartAnimation(bool closing);
// 绘制绘图
extern void DrawESP(ImDrawList *Draw);
// 初始化绘制数据
extern void init_My_drawdata();

class SecureBool {
private:
    uint8_t encryptedValue; // 存储加密后的布尔值

    uint8_t encrypt(bool value) const {
        // 简单加密：将布尔值转换为uint8_t后与某个密钥异或
        return static_cast<uint8_t>(value) ^ 0xAA;
    }

    bool decrypt(uint8_t encryptedValue) const {
        // 简单解密：将存储的值与密钥再次异或得到原始布尔值
        return (encryptedValue ^ 0xAA) != 0;
    }

public:
    SecureBool(bool value) {
        encryptedValue = encrypt(value);
    }

    // 禁止拷贝和赋值操作，防止通过拷贝篡改内存
    SecureBool(const SecureBool&) = delete;
    SecureBool& operator=(const SecureBool&) = delete;

    // 允许移动操作
    SecureBool(SecureBool&& other) noexcept {
        encryptedValue = other.encryptedValue;
        other.encryptedValue = encrypt(false);
    }

    SecureBool& operator=(SecureBool&& other) noexcept {
        if (this != &other) {
            encryptedValue = other.encryptedValue;
            other.encryptedValue = encrypt(false);
        }
        return *this;
    }

    // 访问布尔值的方法
    bool getValue() const {
        return decrypt(encryptedValue);
    }

    void setValue(bool value) {
        encryptedValue = encrypt(value);
    }

    // 转换为布尔值的操作符重载
    operator bool() const {
        return getValue();
    }

    // 重载比较操作符
    bool operator==(const SecureBool& other) const {
        return this->getValue() == other.getValue();
    }

    bool operator!=(const SecureBool& other) const {
        return !(*this == other);
    }
};

#endif //NATIVESURFACE_DRAW_H
