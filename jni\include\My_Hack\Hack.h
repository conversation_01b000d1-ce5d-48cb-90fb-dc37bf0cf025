//
// Created by s2049 on 24-7-14.
//

#ifndef XCJHACK_H
#define XCJHACK_H

#include <cstdio>
#include <cstring>
#include <sys/types.h>
#include "VectorTools.h"

extern uintptr_t libUe4_address;

extern UpData MainUpData;

extern vector<IntoList> MainIntoList;

extern vector<PlayerList> MainPlayerList;

pid_t get_name_pid(char* name);

bool Init_StratHack();

bool initGame_callpthread();

void hack_mainThread();

void UpPlayerData();

int KMPSearch(const string& text, const string& pattern);

#endif //XCJHACK_H
