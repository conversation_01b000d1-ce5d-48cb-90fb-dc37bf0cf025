//
// Created by s2049 on 24-11-20.
// Name: dynamic_Island.h
// Write by XCJ
// 使用状态机架构实现的仿拟iOS的dynamic_Island + 悬浮窗结合
//

#ifndef DYNAMIC_ISLAND_H
#define DYNAMIC_ISLAND_H

#include <string>

#include "imgui.h"

namespace dynamic_Island_XCJ {
    void SetIslandFlage(bool _state);

    void SetTextFlage(bool _state);

    void SetWindowFlage(bool _state);

    void SetIslandPos(ImVec2 _pos);

    void SetIslandSize(ImVec2 _size);

    void SetText(const char* _text);

    void SetText(std::string _text);

    void SetTextColor(ImColor _color);

    void SetTextSize(float _size);

    void SetTextOffset(ImVec2 _offset);

    void Render(ImDrawList* drawList);
}

#endif //DYNAMIC_ISLAND_H
