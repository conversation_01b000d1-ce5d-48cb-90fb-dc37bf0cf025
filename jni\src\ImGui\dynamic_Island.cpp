//
// Created by s2049 on 24-11-20.
// Name: dynamic_Island.h
// Write by XCJ
// 使用状态机架构实现的仿拟iOS的dynamic_Island + 悬浮窗结合
//

#include "dynamic_Island.h"

#include "draw.h"

enum IslandStateFlage {
    Island_FLAG_NONE = 0,
    Island_FLAG_SHOW = 1,
    Island_FLAG_HIDE = 2,
    Island_FLAG_WINDOW = 3,
    Island_FLAG_SHOW_TO_HIDE = 4,
    Island_FLAG_SHOW_TO_WINDOW = 5,
    Island_FLAG_HIDE_TO_SHOW = 6,
    Island_FLAG_HIDE_TO_WINDOW  = 7,
    Island_FLAG_WINDOW_TO_HIDE = 8
};

enum TextStateFlage {
    Text_FLAG_NONE = 0,
    Text_FLAG_SHOW = 1,
    Text_FLAG_HIDE = 2,
    Text_FLAG_SHOW_TO_HIDE = 4,
    Text_FLAG_HIDE_TO_SHOW = 6
};

std::string IslandText;
double IslandStatetime = 0.00f;
double TextStatetime = 0.00f;
double OutIslandSpend = 0.015f;
double InIslandSpend = 0.018f;
double TextSpend = 0.015f;
bool TextIsShow = false;
bool WindowIsShow = false;
bool IslandIsShow = false;
bool NowTextIsShow = false;
bool NowIslandIsShow = false;
float TextSize = 36.0f;
ImColor TextColor = ImColor(235, 235, 235, 255);
ImColor IslandColor = ImColor(0, 0, 0, 255);
ImColor NowIslandColor = ImColor(0, 0, 0, 255);
ImVec2 TextOffset = ImVec2(0, 0);
ImVec2 IslandPos = ImVec2(1000, 0);
ImVec2 IslandSize = ImVec2(400, 80);
ImVec2 WindowSize = ImVec2(1000, 700);
ImVec2 NowIslandPos = ImVec2(0, 0);
ImVec2 NowIslandSize = ImVec2(100, 50);
IslandStateFlage NowIslandState = Island_FLAG_NONE;
TextStateFlage NowTextState = Text_FLAG_NONE;

float easing(float t) {
    return t < 0.5 ? 2 * t * t : 1 - pow(-2 * t + 2, 2) / 2;
}

float lerp(float start, float end, float t) {
    return start + t * (end - start);
}

void dynamic_Island_XCJ::SetWindowFlage(bool _state) {
    if (WindowIsShow != _state) {
        WindowIsShow = _state;
        if (_state) {
            switch (NowIslandState) {
                case Island_FLAG_HIDE:
                    NowIslandState = Island_FLAG_HIDE_TO_WINDOW;
                    break;
                case Island_FLAG_SHOW:
                    IslandStatetime = 0.00f;
                    NowIslandState = Island_FLAG_SHOW_TO_WINDOW;
                break;
                default:
                    NowIslandState = Island_FLAG_HIDE_TO_WINDOW;
                    break;
            }
        } else {
            switch (NowIslandState) {
                case Island_FLAG_WINDOW:
                    NowIslandState = Island_FLAG_WINDOW_TO_HIDE;
                    break;
                case Island_FLAG_SHOW_TO_WINDOW:
                    NowIslandState = Island_FLAG_WINDOW_TO_HIDE;
                    break;
                case Island_FLAG_HIDE_TO_WINDOW:
                    NowIslandState = Island_FLAG_WINDOW_TO_HIDE;
                    break;
                default:
                    break;
            }
        }
    }
}

void dynamic_Island_XCJ::SetTextFlage(bool _state) {
    if (TextIsShow != _state) {
        TextIsShow = _state;
        if (_state) {
            switch (NowTextState) {
                case Text_FLAG_HIDE:
                    NowTextState = Text_FLAG_HIDE_TO_SHOW;
                    break;
                case Text_FLAG_SHOW_TO_HIDE:
                    NowTextState = Text_FLAG_HIDE_TO_SHOW;
                    break;
                default:
                    break;
            }
        } else {
            switch (NowTextState) {
                case Text_FLAG_SHOW:
                    NowTextState = Text_FLAG_SHOW_TO_HIDE;
                    break;
                case Text_FLAG_SHOW_TO_HIDE:
                    NowTextState = Text_FLAG_SHOW_TO_HIDE;
                    break;
                case Text_FLAG_HIDE_TO_SHOW:
                    NowTextState = Text_FLAG_SHOW_TO_HIDE;
                default:
                    break;
            }
        }
    }
}

void dynamic_Island_XCJ::SetIslandFlage(bool _state) {
    if (IslandIsShow != _state) {
        IslandIsShow = _state;
        if (_state) {
            switch (NowIslandState) {
                case Island_FLAG_HIDE:
                    NowIslandState = Island_FLAG_HIDE_TO_SHOW;
                    break;
                case Island_FLAG_SHOW_TO_HIDE:
                    NowIslandState = Island_FLAG_HIDE_TO_SHOW;
                    break;
                case Island_FLAG_WINDOW:
                    break;
                case Island_FLAG_WINDOW_TO_HIDE:
                    break;
                default:
                    break;
            }
        } else {
            switch (NowIslandState) {
                case Island_FLAG_SHOW:
                    NowIslandState = Island_FLAG_SHOW_TO_HIDE;
                case Island_FLAG_HIDE_TO_SHOW:
                    NowIslandState = Island_FLAG_SHOW_TO_HIDE;
                    break;
                case Island_FLAG_WINDOW:
                    break;
                default:
                    break;
            }
        }
    }
}

void dynamic_Island_XCJ::SetText(const char *_text) {
    IslandText = _text;
}

void dynamic_Island_XCJ::SetText(std::string _text) {
    IslandText = std::move(_text);
}

void dynamic_Island_XCJ::SetTextColor(ImColor _color) {
    TextColor = _color;
}

void dynamic_Island_XCJ::SetTextOffset(ImVec2 _offset) {
    TextOffset = _offset;
}

void dynamic_Island_XCJ::SetIslandPos(ImVec2 _pos) {
    IslandPos = _pos;
}

void dynamic_Island_XCJ::SetIslandSize(ImVec2 _size) {
    IslandSize = _size;
}

void dynamic_Island_XCJ::SetTextSize(float _size) {
    TextSize = _size;
}

// 状态机主要逻辑和渲染函数
void dynamic_Island_XCJ::Render(ImDrawList* drawList) {
    if (NowIslandState == Island_FLAG_NONE) {
        NowIslandState = Island_FLAG_HIDE;
    }
    if (NowTextState == Text_FLAG_NONE) {
        NowTextState = Text_FLAG_HIDE;
    }


    if (IslandText.empty())
        SetTextFlage(false);
    else
        SetTextFlage(true);

    NowIslandPos.x = IslandPos.x;
    NowIslandPos.y = IslandPos.y;

    switch (NowIslandState) {
        case Island_FLAG_HIDE_TO_SHOW:
            SetTextFlage(true);
            NowIslandIsShow = true;
            IslandStatetime += InIslandSpend;
            if (IslandStatetime >= 1.00f) {
                NowIslandState = Island_FLAG_SHOW;
                IslandStatetime = 1.00f;
            }
            NowIslandSize.x = lerp(0, IslandSize.x, easing(IslandStatetime));
            NowIslandSize.y = lerp(0, IslandSize.y, easing(IslandStatetime));
            break;

        case Island_FLAG_SHOW_TO_HIDE:
            SetTextFlage(false);
            IslandStatetime -= OutIslandSpend;
            if (IslandStatetime <= 0.00f) {
                NowIslandState = Island_FLAG_HIDE;
                IslandStatetime = 0.00f;
                NowIslandIsShow = false;
            }
            NowIslandSize.x = lerp(0, IslandSize.x, easing(IslandStatetime));
            NowIslandSize.y = lerp(0, IslandSize.y, easing(IslandStatetime));
            break;

        case Island_FLAG_HIDE_TO_WINDOW:
            SetTextFlage(true);
            NowIslandIsShow = true;
            IslandStatetime += InIslandSpend;
            if (IslandStatetime >= 1.00f) {
                NowIslandState = Island_FLAG_WINDOW;
                IslandStatetime = 1.00f;
            }
            NowIslandColor.Value.x = lerp(IslandColor.Value.x, ImGui::GetStyle().Colors[ImGuiCol_WindowBg].x, easing(IslandStatetime));
            NowIslandColor.Value.y = lerp(IslandColor.Value.y, ImGui::GetStyle().Colors[ImGuiCol_WindowBg].y, easing(IslandStatetime));
            NowIslandColor.Value.z = lerp(IslandColor.Value.z, ImGui::GetStyle().Colors[ImGuiCol_WindowBg].z, easing(IslandStatetime));
            NowIslandSize.x = lerp(0, WindowSize.x + 10, easing(IslandStatetime));
            NowIslandSize.y = lerp(0, WindowSize.y + 5, easing(IslandStatetime));
            break;

        case Island_FLAG_SHOW_TO_WINDOW:
            SetTextFlage(true);
            IslandStatetime += InIslandSpend;
            if (IslandStatetime >= 1.00f) {
                NowIslandState = Island_FLAG_WINDOW;
                IslandStatetime = 1.00f;
            }
            NowIslandColor.Value.x = lerp(IslandColor.Value.x, ImGui::GetStyle().Colors[ImGuiCol_WindowBg].x, easing(IslandStatetime));
            NowIslandColor.Value.y = lerp(IslandColor.Value.y, ImGui::GetStyle().Colors[ImGuiCol_WindowBg].y, easing(IslandStatetime));
            NowIslandColor.Value.z = lerp(IslandColor.Value.z, ImGui::GetStyle().Colors[ImGuiCol_WindowBg].z, easing(IslandStatetime));
            NowIslandSize.x = lerp(IslandSize.x, WindowSize.x + 10, easing(IslandStatetime));
            NowIslandSize.y = lerp(IslandSize.y, WindowSize.y + 5, easing(IslandStatetime));
        break;

        case Island_FLAG_WINDOW_TO_HIDE:
            SetTextFlage(false);
            IslandStatetime -= OutIslandSpend;
            if (IslandStatetime <= 0.00) {
                NowIslandState = Island_FLAG_HIDE;
                IslandStatetime = 0.00f;
                IslandIsShow = false;
                NowIslandIsShow = false;
            }
            NowIslandColor.Value.x = lerp(IslandColor.Value.x, ImGui::GetStyle().Colors[ImGuiCol_WindowBg].x, easing(IslandStatetime));
            NowIslandColor.Value.y = lerp(IslandColor.Value.y, ImGui::GetStyle().Colors[ImGuiCol_WindowBg].y, easing(IslandStatetime));
            NowIslandColor.Value.z = lerp(IslandColor.Value.z, ImGui::GetStyle().Colors[ImGuiCol_WindowBg].z, easing(IslandStatetime));
            NowIslandSize.x = lerp(0, WindowSize.x + 10, easing(IslandStatetime));
            NowIslandSize.y = lerp(0, WindowSize.y + 5, easing(IslandStatetime));
        default:
            break;
    }

    switch (NowTextState) {
        case Text_FLAG_HIDE_TO_SHOW:
            NowTextIsShow = true;
            TextStatetime += TextSpend;
            if (TextStatetime >= 1.00f) {
                NowTextState = Text_FLAG_SHOW;
                TextStatetime = 1.00f;
            }
            TextColor = ImColor(255, 255, 255, (unsigned int)(255 * TextStatetime));
            break;

        case Text_FLAG_SHOW_TO_HIDE:
            TextStatetime -= TextSpend;
            if (TextStatetime <= 0.00f) {
                NowTextState = Text_FLAG_HIDE;
                TextStatetime = 0.00f;
                NowTextIsShow = false;
            }
            TextColor = ImColor(255, 255, 255, (unsigned int)(255 * TextStatetime));
            break;

        default:
            break;
    }

    if (NowIslandIsShow) {
        drawList->AddRectFilled(ImVec2(NowIslandPos.x - NowIslandSize.x / 2.0f, NowIslandPos.y), ImVec2(NowIslandPos.x + NowIslandSize.x / 2.0f, NowIslandPos.y + NowIslandSize.y), NowIslandColor, 40.0f);
    }

    if (NowTextIsShow) {
        ImVec2 textSize = ImGui::CalcTextSize(IslandText.c_str(), nullptr, TextSize);
        ImVec2 textPos = ImVec2(IslandPos.x - textSize.x * 0.5 + TextOffset.x, NowIslandPos.y + IslandSize.y * 0.5 - textSize.y * 0.5 + TextOffset.y);
        drawList->AddText(nullptr, TextSize, textPos, TextColor, IslandText.c_str());
    }
}