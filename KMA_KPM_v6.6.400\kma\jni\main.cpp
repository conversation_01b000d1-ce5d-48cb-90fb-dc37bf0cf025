#include <iostream>
#include <thread>
#include "driver.h"

// 运行在堆内存方式
int main(int argc, char *argv[])
{
	//new对象
	Driver *driver = new Driver();
	
	// 程序运行在CPU0上
	driver->cpuset(0);
	
	// 程序随机运行在CPU0-4上，并且选择使用率最低的那颗CPU核心
	driver->cpuset(0, 4);
	
	// 获取进程pid，该方法从内核层获取pid，无需应用层遍历proc文件夹
	pid_t pid = driver->get_pid("bin.mt.plus");
	
	// 获取mtio线程pid(tid)
	pid_t pid2 = driver->get_pid("bin.mt.plus", "mtio");
		
	// 初始化pid，指定创建名为driver的对象作用于该pid对应的进程，如果初始化失败则返回false，就没有必要进行读写了，该函数仅检测驱动初始化状态，不检查pid情况
	if (!driver->initpid(pid)) return 0;
	
	/*
	获取so模块地址
	7814006000-7814043000 r-xp 00000000 fe:0c 2891631                        /data/app/~~Ga0Qoijfa6Srj16BrdylTQ==/bin.mt.plus-2g1bTpkzPU3q-oLrAsFU-A==/lib/arm64/libmt1.so
	在一定程度上，应用进程每个版本的模块长度(大小)都是固定，即0x7814043000-0x7814006000=0x3D000
	get_module_base方法在内核层中获取模块地址，参数: 进程pid 模块名称 模块长度
	uint64_t so = driver->get_module_base(pid, "libmt1.so", 0x3D000);
	
	该函数有一个重载方法，当不传入模块长度时，默认取加载的第一行作为模块头
	uint64_t so = driver->get_module_base(pid, "libmt1.so");
	*/
	
	// 读取libmt1.so模块地址
	uint64_t so = driver->get_module_base(pid, "libmt1.so");
		
		
	volatile uint64_t val;
	
	volatile uint64_t val2;
	
	//向已映射的虚拟地址写入数据 类型float 写入1.0
	driver->write<float>(0x7C85227FFC, 1.0f);
	
	//向已映射的虚拟地址写入数据 类型int 写入100
	driver->write<int>(0x7C85227FFC, 100);
	
	//向已映射的虚拟地址写入数据 类型char 写入A
	driver->write<char>(0x7C85227FFC, 'A');
	
	struct Point
	{
	    Point(float x, float y, float z)
	    {
	        this->x = x;
	        this->y = y;
	        this->z = z;
	    }
	    float x;
	    float y;
	    float z;
	};
	
	Point point(1,2,3);
	
	//向已映射的虚拟地址写入数据 类型结构体
	driver->write(0x7C85227FFC, &point, sizeof(point));
	
	/*
	// 读取地址上的值
	float fval = driver->read<float>(0x7C85227FFC);
	
	fval = driver->read_safe<float>(0x7C85227FFC);
	
	// 读取数组
	float aval[16*4];
	driver->read(0x7C85227FFC, &aval, sizeof(aval));
	
	driver->read_safe(0x7C85227FFC, &aval, sizeof(aval));
	
	// 读取结构体
	driver->read(0x7C85227FFC, &point, sizeof(point));
	
	driver->read_safe(0x7C85227FFC, &point, sizeof(point));
	
	*/
	
	//
	val = driver->read<uint64_t>(so + 0x478);
	
	val2 = driver->read_safe<uint64_t>(so + 0x4302C);
			
	// 创建一个线程
	thread(
	[&]
	{
	    // 一个进程(线程)可以使用多个对象，但一个对象仅用于一个(进程)线程。
	    // new一个对象
	    Driver *driver2 = new Driver();
	    
	    // 绑定该线程在小核运行，能不用大核尽量不用大核，防止大核资源不够分配给目标应用，PS:不要想着大核就一定快。
	    driver2->cpuset(0, 2);
	    
	    // 目标pid可以和主进程复用，这里仅作演示效果，能用局部变量，尽量用局部变量。
	    // 获取进程pid，该方法从内核层获取pid，无需应用层遍历proc文件夹
	    pid_t pid = driver2->get_pid("bin.mt.plus");
	    
	    // 初始化pid
	    if (!driver->initpid(pid)) return 0;
	    
	    // 模块地址也可以和主进程复用
	    // 读取libmt1.so模块地址
	    uint64_t so = driver->get_module_base(pid, "libmt1.so");
	    
	    volatile uint64_t val;
	    
	    for (int i = 0; i < 100; i++)
	        val = driver->read<uint64_t>(so);
	        //val = driver->read_safe<uint64_t>(so);
	    printf("this_thread pid: %d so: %lX val: %lX\n", pid, so, val);
	}
	).detach();
	
	// 等待线程跑完或者可以使用join等待
	this_thread::sleep_for(100ms);
	
	printf("pid: %d so: %lX val: %lX val2: %lX\n", pid, so, val, val2);
}

// 运行在栈内存方式
int main2(int argc, char *argv[])
{
	// 创建对象
	Driver dv;
	
	Driver *driver = &dv;
	
	// 程序运行在CPU0上
	driver->cpuset(0);
	
	// 程序随机运行在CPU0-4上，并且选择使用率最低的那颗CPU核心
	driver->cpuset(0, 4);
	
	// 获取进程pid，该方法从内核层获取pid，无需应用层遍历proc文件夹
	pid_t pid = driver->get_pid("bin.mt.plus");
	
	// 获取mtio线程pid(tid)
	pid_t pid2 = driver->get_pid("bin.mt.plus", "mtio");
		
	// 初始化pid，指定创建名为driver的对象作用于该pid对应的进程，如果初始化失败则返回false，就没有必要进行读写了，该函数仅检测驱动初始化状态，不检查pid情况
	if (!driver->initpid(pid)) return 0;
	
	/*
	获取so模块地址
	7814006000-7814043000 r-xp 00000000 fe:0c 2891631                        /data/app/~~Ga0Qoijfa6Srj16BrdylTQ==/bin.mt.plus-2g1bTpkzPU3q-oLrAsFU-A==/lib/arm64/libmt1.so
	在一定程度上，应用进程每个版本的模块长度(大小)都是固定，即0x7814043000-0x7814006000=0x3D000
	get_module_base方法在内核层中获取模块地址，参数: 进程pid 模块名称 模块长度
	uint64_t so = driver->get_module_base(pid, "libmt1.so", 0x3D000);
	
	该函数有一个重载方法，当不传入模块长度时，默认取加载的第一行作为模块头
	uint64_t so = driver->get_module_base(pid, "libmt1.so");
	*/
	
	// 读取libmt1.so模块地址
	uint64_t so = driver->get_module_base(pid, "libmt1.so");
		
		
	volatile uint64_t val;
	
	volatile uint64_t val2;
	
	//向已映射的虚拟地址写入数据 类型float 写入1.0
	driver->write<float>(0x7C85227FFC, 1.0f);
	
	//向已映射的虚拟地址写入数据 类型int 写入100
	driver->write<int>(0x7C85227FFC, 100);
	
	//向已映射的虚拟地址写入数据 类型char 写入A
	driver->write<char>(0x7C85227FFC, 'A');
	
	struct Point
	{
	    Point(float x, float y, float z)
	    {
	        this->x = x;
	        this->y = y;
	        this->z = z;
	    }
	    float x;
	    float y;
	    float z;
	};
	
	Point point(1,2,3);
	
	//向已映射的虚拟地址写入数据 类型结构体
	driver->write(0x7C85227FFC, &point, sizeof(point));
	
	/*
	// 读取地址上的值
	float fval = driver->read<float>(0x7C85227FFC);
	
	fval = driver->read_safe<float>(0x7C85227FFC);
	
	// 读取数组
	float aval[16*4];
	driver->read(0x7C85227FFC, &aval, sizeof(aval));
	
	driver->read_safe(0x7C85227FFC, &aval, sizeof(aval));
	
	// 读取结构体
	driver->read(0x7C85227FFC, &point, sizeof(point));
	
	driver->read_safe(0x7C85227FFC, &point, sizeof(point));
	
	*/
	
	//
	val = driver->read<uint64_t>(so + 0x478);
	
	val2 = driver->read_safe<uint64_t>(so + 0x4302C);
			
	printf("pid: %d so: %lX val: %lX val2: %lX\n", pid, so, val, val2);
}