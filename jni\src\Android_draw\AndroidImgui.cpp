#include <android/native_window.h>
#include "AndroidImgui.h"
#include "imgui.h"
#include "my_imgui_impl_android.h"
#include "stb_image.h"

bool AndroidImgui::Init_Render(ANativeWindow *window, float width, float height)
{
    m_Window = window;
    m_Width = width;
    m_Height = height;

    ANativeWindow_acquire(window);
    Create();

    // Setup Dear ImGui context
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO &io = ImGui::GetIO();

    ImGuiStyle &style = ImGui::GetStyle();

    // 控件缩放比例
    style.ScaleAllSizes(2.0f); // 根据需要调整缩放比例

    // 设置窗口背景透明度
    ImGui::SetNextWindowBgAlpha(1.0f);

    // 使用ImGui原生亮色主题（文字更清晰）
    ImGui::StyleColorsLight();

    // 设置其他样式参数
    style.WindowTitleAlign = ImVec2(0.5f, 0.5f); // 主窗口标题居中
    style.WindowRounding = 9.0f;                 // 主窗口圆角半径
    style.WindowBorderSize = 0.0f;               // 主窗口边框粗细
    style.ChildRounding = 10.0f;                 // 子窗口圆角半径
    style.ChildBorderSize = 0.5f;                // 子窗口边框粗细
    style.FrameRounding = 10.0f;                 // 控件圆角半径
    style.FrameBorderSize = 0.5f;                // 控件边框粗细
    style.ScrollbarRounding = 12.0f;             // 滚动条圆角半径
    style.ScrollbarSize = 30.0f;                 // 滚动条宽度
    style.GrabMinSize = 15.0f;                   // 滑块最小宽度
    style.GrabRounding = 5.0f;                   // 滑块圆角半径

    io.IniFilename = nullptr;
    io.LogFilename = nullptr;
    io.DisplaySize = {width, height};

    My_ImGui_ImplAndroid_Init(window);

    Setup();
    return true;
}

void AndroidImgui::NewFrame(bool resize)
{
    PrepareFrame(resize);
    My_ImGui_ImplAndroid_NewFrame(resize);
    ImGui::NewFrame();
}

void AndroidImgui::EndFrame()
{
    ImGui::Render();
    Render(ImGui::GetDrawData());
}

void AndroidImgui::Shutdown()
{
    for (auto &texture : m_Textures)
    {
        RemoveTexture(texture);
    }
    m_Textures.clear();
    PrepareShutdown();
    My_ImGui_ImplAndroid_Shutdown();
    ImGui::DestroyContext();
    Cleanup();
    ANativeWindow_release(m_Window);
}

BaseTexData *AndroidImgui::LoadTextureData(const std::function<unsigned char *(BaseTexData *)> &loadFunc)
{
    BaseTexData tex_data{};

    tex_data.Channels = 4;
    unsigned char *image_data = loadFunc(&tex_data);
    if (image_data == nullptr)
        return nullptr;

    auto result = LoadTexture(&tex_data, image_data);

    stbi_image_free(image_data);

    if (result)
    {
        m_Textures.push_back(result);
    }
    return result;
}

BaseTexData *AndroidImgui::LoadTextureFromFile(const char *filepath)
{
    return LoadTextureData([filepath](BaseTexData *tex_data)
                           { return stbi_load(filepath, &tex_data->Width, &tex_data->Height, nullptr, tex_data->Channels); });
}

BaseTexData *AndroidImgui::LoadTextureFromMemory(void *data, int len)
{
    return LoadTextureData([data, len](BaseTexData *tex_data)
                           { return stbi_load_from_memory((const stbi_uc *)data, len, &tex_data->Width, &tex_data->Height, nullptr, tex_data->Channels); });
}

void AndroidImgui::DeleteTexture(BaseTexData *tex_data)
{
    RemoveTexture(tex_data);
    auto it = std::find(m_Textures.begin(), m_Textures.end(), tex_data);
    if (it != m_Textures.end())
    {
        m_Textures.erase(it);
    }
}
