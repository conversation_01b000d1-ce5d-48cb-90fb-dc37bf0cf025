LOCAL_PATH:= $(call my-dir)


include $(CLEAR_VARS)
LOCAL_MODULE := test.sh

LOCAL_CFLAGS := -std=c++17 -fexceptions -frtti
LOCAL_CPPFLAGS := -std=c++17 -fexceptions -frtti

LOCAL_CFLAGS += -Wno-error=format-security -w
LOCAL_CFLAGS += -fno-rtti -fno-exceptions -fpermissive -fvisibility=hidden
LOCAL_CFLAGS += -O2 -fPIC -Wall
LOCAL_CFLAGS += -DVK_USE_PLATFORM_ANDROID_KHR
LOCAL_CFLAGS += -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES
LOCAL_CPPFLAGS += -DVK_USE_PLATFORM_ANDROID_KHR
LOCAL_CPPFLAGS += -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES
LOCAL_CPPFLAGS += -Wno-error=format-security -fpermissive -w -Werror -s 
LOCAL_CPPFLAGS += -fno-rtti -fno-exceptions -fms-extensions -Wno-error=c++11-narrowing -fvisibility=hidden

#引入头文件到全局#
LOCAL_C_INCLUDES += $(LOCAL_PATH)/include
LOCAL_C_INCLUDES += $(LOCAL_PATH)/include/Android_draw
LOCAL_C_INCLUDES += $(LOCAL_PATH)/include/Android_Graphics
LOCAL_C_INCLUDES += $(LOCAL_PATH)/include/Android_my_imgui
LOCAL_C_INCLUDES += $(LOCAL_PATH)/include/Android_touch
LOCAL_C_INCLUDES += $(LOCAL_PATH)/include/My_Utils
LOCAL_C_INCLUDES += $(LOCAL_PATH)/include/My_Tools
LOCAL_C_INCLUDES += $(LOCAL_PATH)/include/My_Hack
LOCAL_C_INCLUDES += $(LOCAL_PATH)/include/ImGui
LOCAL_C_INCLUDES += $(LOCAL_PATH)/include/ImGui/backends
LOCAL_C_INCLUDES += $(LOCAL_PATH)/include/ImGui/My_font
LOCAL_C_INCLUDES += $(LOCAL_PATH)/include/native_surface
LOCAL_C_INCLUDES += $(LOCAL_PATH)/include/Validate
LOCAL_C_INCLUDES += $(LOCAL_PATH)/include/Audio

FILE_LIST += $(wildcard $(LOCAL_PATH)/src/*.c*)
FILE_LIST += $(wildcard $(LOCAL_PATH)/src/Android_draw/*.c*)
FILE_LIST += $(wildcard $(LOCAL_PATH)/src/Android_Graphics/*.c*)
FILE_LIST += $(wildcard $(LOCAL_PATH)/src/Android_my_imgui/*.c*)
FILE_LIST += $(wildcard $(LOCAL_PATH)/src/Android_touch/*.c*)
FILE_LIST += $(wildcard $(LOCAL_PATH)/src/My_Utils/*.c*)
FILE_LIST += $(wildcard $(LOCAL_PATH)/src/My_Tools/*.c*)
FILE_LIST += $(wildcard $(LOCAL_PATH)/src/My_Hack/*.c*)
FILE_LIST += $(wildcard $(LOCAL_PATH)/src/ImGui/*.c*)
FILE_LIST += $(wildcard $(LOCAL_PATH)/src/ImGui/backends/*.c*)

LOCAL_SRC_FILES := $(FILE_LIST:$(LOCAL_PATH)/%=%)

# 链接KMA驱动静态库
LOCAL_LDLIBS := -llog -landroid -lEGL -lGLESv2 -lGLESv3 -lz -lOpenSLES
LOCAL_LDLIBS += $(LOCAL_PATH)/libs/libkma_driver.a

include $(BUILD_EXECUTABLE)