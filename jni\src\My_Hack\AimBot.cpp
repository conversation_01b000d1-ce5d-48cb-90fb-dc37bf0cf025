//
// Created by s2049 on 24-7-17.
//

#include "AimBot.h"

#include <unistd.h>

#include "ConfigManager.h"
#include "GameTools.h"
#include "Hack.h"
#include "TouchHelperA.h"
#include "draw.h"
#include "log.h"
#include "timer.h"

std::unordered_map<int, float> WeaponMap;

float GetWeaponId(int WeaponId) {
  if (WeaponMap.find(WeaponId) != WeaponMap.end()) {
    return WeaponMap[WeaponId];
  }
  WeaponMap[WeaponId] = 1.0;
  return WeaponMap[WeaponId];
}

float GetScope(float Scope) {
  switch (static_cast<int>(90 / Scope)) {
  case 0:
    return 0.5f;
  case 1:
    return 0.5f;
  case 2:
    return 0.28f;
  case 3:
    return 0.24f;
  case 4:
    return 0.19f;
  case 5:
    return 0.18f;
  case 6:
    return 0.15f;
  case 7:
    return 0.13f;
  default:
    return 0.1f;
  }
}

int Aim_At = -1;
int Predict_At = -1;
long looptime = -1;
string Predict_At_name;
string Find_aim_name = "Not_Found";
double Mdistance;
double speed;
Vec2 mspeed;
Timer aimtiem;
Vec3 oldBone;
Vec3 predictBone;
Vec3 AimBone, FindAimBone;

int Find_Aim() {
  if (!ConfigManager::Settings.isAimbot) {
    return -1;
  }

  int min_Range = ConfigManager::Settings.Range;
  int min_At = -1;

  for (int i = 0; i < MainPlayerList.size(); i++) {
    auto Players = MainPlayerList[i];

    if (Players.player_bone.Head.ScreenPos.x > 0 && Players.player_bone.Head.ScreenPos.x < screen_x && Players.player_bone.Head.ScreenPos.y > 0 && Players.player_bone.Head.ScreenPos.y < screen_y) {
      if (ConfigManager::Settings.isNotAimRobot && Players.IsBot)
        continue;
      if (ConfigManager::Settings.isKnockdown && Players.Health <= 0)
        continue;
      if (Players.IsBot) {
        if (MainUpData.IsAiming) {
          if (Players.Distance > ConfigManager::Settings.aimBotDistance)
            continue;
        } else {
          if (Players.Distance > ConfigManager::Settings.NotaimBotDistance)
            continue;
        }
      } else {
        if (MainUpData.IsAiming) {
          if (Players.Distance > ConfigManager::Settings.aimDistance)
            continue;
        } else {
          if (Players.Distance > ConfigManager::Settings.NotaimDistance)
            continue;
        }
      }

      BoneStruct *bones[] = {
          &Players.player_bone.Head,
          &Players.player_bone.Chest,
          &Players.player_bone.Pelvis,
          &Players.player_bone.Left_Shoulder,
          &Players.player_bone.Right_Shoulder,
          &Players.player_bone.Left_Elbow,
          &Players.player_bone.Right_Elbow,
          &Players.player_bone.Left_Wrist,
          &Players.player_bone.Right_Wrist,
          &Players.player_bone.Left_Thigh,
          &Players.player_bone.Right_Thigh,
          &Players.player_bone.Left_Knee,
          &Players.player_bone.Right_Knee,
          &Players.player_bone.Left_Ankle,
          &Players.player_bone.Right_Ankle,
      };
      int boneCount = 15;
      if (ConfigManager::Settings.isContinued && Find_aim_name != "Not_Found") {
        if (!Find_aim_name.compare(Players.PlayerName)) {
          bool IsCansSee = false;
          for (int j = 0; j < boneCount; ++j) {
            if (bones[j]->CanSee) {
              IsCansSee = true;
              MainPlayerList[i].LockBone = *bones[j];
              min_At = i;
              break;
            }
          }
          if (!IsCansSee) {
            Find_aim_name = "Not_Found";
            break;
          }
          break;
        }
        continue;
      }
      bool IsCansSee = false;
      for (int j = 0; j < boneCount; ++j) {
        if (bones[j]->CanSee) {
          IsCansSee = true;
          MainPlayerList[i].LockBone = *bones[j];
          break;
        }
      }
      if (!IsCansSee)
        continue;
      float Screen_Distance = 0;
      float boneMinRangeDistance = 9999999;
      for (int j = 0; j < boneCount; ++j) {
        float tempDistance = sqrt(pow(screen_x / 2 - bones[j]->ScreenPos.x, 2) + pow(screen_y / 2 - bones[j]->ScreenPos.y, 2));
        if (bones[j]->CanSee && boneMinRangeDistance > tempDistance) {
          boneMinRangeDistance = tempDistance;
        }
      }
      switch (ConfigManager::Settings.LockPos) {
      case 0:
        Screen_Distance = boneMinRangeDistance;
        break;
      case 1:
        if (ConfigManager::Settings.isNotAimSmoke) {
          if (!Players.player_bone.Head.CanSee) {
            continue;
          }
        }
        Screen_Distance = sqrt(pow(screen_x / 2 - Players.player_bone.Head.ScreenPos.x, 2) + pow(screen_y / 2 - Players.player_bone.Head.ScreenPos.y, 2));
        break;
      case 2:
        if (ConfigManager::Settings.isNotAimSmoke) {
          if (!Players.player_bone.Chest.CanSee) {
            continue;
          }
        }
        Screen_Distance = sqrt(pow(screen_x / 2 - Players.player_bone.Chest.ScreenPos.x, 2) + pow(screen_y / 2 - Players.player_bone.Chest.ScreenPos.y, 2));
        break;
      case 3:
        if (ConfigManager::Settings.isNotAimSmoke) {
          if (!Players.player_bone.Pelvis.CanSee) {
            continue;
          }
        }
        Screen_Distance = sqrt(pow(screen_x / 2 - Players.player_bone.Pelvis.ScreenPos.x, 2) + pow(screen_y / 2 - Players.player_bone.Pelvis.ScreenPos.y, 2));
        break;
      }
      if (Screen_Distance < min_Range) {
        min_Range = Screen_Distance;
        min_At = i;
      }
    }
  }
  if (min_At == -1) {
    Find_aim_name = "Not_Found";
  } else {
    Find_aim_name = MainPlayerList[min_At].PlayerName;
  }
  return min_At;
}

void AimBotStart() {
  bool IsAim;
  Aim_At = Find_Aim();
  switch (ConfigManager::Settings.LockMode) {
  case 0:
    IsAim = MainUpData.IsFiring;
    break;
  case 1:
    IsAim = MainUpData.IsAiming;
    break;
  case 2:
    IsAim = MainUpData.IsFiring || MainUpData.IsAiming;
    break;
  case 3:
    IsAim = MainUpData.IsFiring && MainUpData.IsAiming;
    break;
  default:
    IsAim = false;
    LOGE("No LockMode");
    break;
  }
  if (Aim_At != -1) {
    auto Players = MainPlayerList[Aim_At];
    BoneStruct *bones[] = {
        &Players.player_bone.Head,
        &Players.player_bone.Chest,
        &Players.player_bone.Pelvis,
        &Players.player_bone.Left_Shoulder,
        &Players.player_bone.Right_Shoulder,
        &Players.player_bone.Left_Elbow,
        &Players.player_bone.Right_Elbow,
        &Players.player_bone.Left_Wrist,
        &Players.player_bone.Right_Wrist,
        &Players.player_bone.Left_Thigh,
        &Players.player_bone.Right_Thigh,
        &Players.player_bone.Left_Knee,
        &Players.player_bone.Right_Knee,
        &Players.player_bone.Left_Ankle,
        &Players.player_bone.Right_Ankle,
    };
    int boneCount = 15;

    for (int i = 0; i < boneCount; i++) {
      if (bones[i]->CanSee) {
        Players.LockBone = *bones[i];
        break;
      }
    }

    switch (ConfigManager::Settings.LockPos) {
    case 0:
      AimBone = Players.LockBone.Pos;
      break;
    case 1:
      AimBone = Players.player_bone.Head.Pos;
      break;
    case 2:
      AimBone = Players.player_bone.Chest.Pos;
      break;
    case 3:
      AimBone = Players.player_bone.Pelvis.Pos;
      break;
    }

    if (Predict_At |= Aim_At) {
      Predict_At = Aim_At;
    }

    if (!Predict_At_name.compare(Players.PlayerName)) {
      if (!VectorTools::CompareVector3(oldBone, Players.player_bone.Head.Pos)) {
        float bulletFlyTime = Players.Distance / MainUpData.bulletSpeed * ConfigManager::Settings.Predict * 100.0f;
        // printf("bulletFlyTime: %f\n", bulletFlyTime);
        predictBone.x = (Players.VelocitySafety.x * bulletFlyTime);
        predictBone.y = (Players.VelocitySafety.y * bulletFlyTime);
        predictBone.z = (Players.VelocitySafety.z * bulletFlyTime);
      } else {
        predictBone = {0, 0, 0};
      }
      oldBone = Players.player_bone.Head.Pos;
    } else {
      oldBone = Players.player_bone.Head.Pos;
      Predict_At_name = Players.PlayerName;
      predictBone = {0, 0, 0};
    }
    if (IsAim) {
      AimBone.x += predictBone.x;
      AimBone.y += predictBone.y;
      AimBone.z += predictBone.z;
      touch_information.Accuracy_X = MainUpData.IsAiming ? ConfigManager::Settings.accuracy_X : ConfigManager::Settings.accuracy_X * 0.5;
      touch_information.Accuracy_Y = MainUpData.IsAiming ? ConfigManager::Settings.accuracy_Y : ConfigManager::Settings.accuracy_Y * 0.5;
      auto SelfCameraPos = MainUpData.SelfViewInfo.Location;
      if (MainUpData.SelfWeaponType != 103012 && MainUpData.SelfWeaponType != 103002 && MainUpData.SelfWeaponType != 103003 && MainUpData.SelfWeaponType != 103015 && MainUpData.SelfWeaponType != 103011 && MainUpData.SelfWeaponType != 103001 && MainUpData.SelfWeaponType != 103016 && MainUpData.SelfWeaponType != 103008) {
        if (MainUpData.IsFiring) {
          const auto weaponId = GetWeaponId(MainUpData.SelfWeaponType);
          const auto distance = Players.Distance;
          if (MainUpData.SelfState == 288 || MainUpData.SelfState == 1112 || MainUpData.SelfState == 4384 || MainUpData.SelfState == 5408) {
            SelfCameraPos.z += distance * ConfigManager::Settings.Down * weaponId * ConfigManager::Settings.Squat_Down;
          } else if (MainUpData.SelfState == 320 || MainUpData.SelfState == 1344) {
            SelfCameraPos.z += distance * ConfigManager::Settings.Down * weaponId * ConfigManager::Settings.Lying_Down;
          } else {
            SelfCameraPos.z += distance * ConfigManager::Settings.Down * weaponId;
          }
        }
      } else {
        const auto distance = Players.Distance;
        SelfCameraPos.z += distance * ConfigManager::Settings.Down;
      }
      touch_information.Scal = 80 / MainUpData.SelfViewInfo.FOV;
      touch_information.MouseCoordinate = {MainUpData.SelfControlRotation.Pitch, MainUpData.SelfControlRotation.Yaw};
      touch_information.AimingCoordinates = touchdriven.FastAtan2(AimBone, SelfCameraPos);
      touchdriven.setAimIo(true);
    } else {
      touchdriven.setAimIo(false);
      if (ConfigManager::Settings.isContinued) {
        Find_aim_name = "Not_Found";
      }
    }
  } else {
    touchdriven.setAimIo(false);
    if (ConfigManager::Settings.isContinued) {
      Find_aim_name = "Not_Found";
    }
  }
}