#ifndef SELINUX_TOOLS_H
#define SELINUX_TOOLS_H

#include <string>
#include <vector>

enum class DeviceArchitecture {
    ArmeabiV7a,
    Arm64V8a,
    X86,
    X86_64,
    Unknown,
};

namespace LinuxSystem_Tools {
    extern std::string libcPath;
    extern std::string linkerPath;
    extern std::string libDLPath;
    extern DeviceArchitecture arch;
    extern int sdkVersion;

    void setupSystemLibs();
    int isSELinuxEnabled();
    void disableSELinux();
    void enableSELinux();
    void launchApp(const std::string& appLaunchActivity);
    void* getModuleBaseAddr(pid_t pid, const std::string& moduleName);
    void* getRemoteFuncAddr(pid_t pid, const std::string& moduleName, void* localFuncAddr);
};

#endif // SELINUX_TOOLS_H