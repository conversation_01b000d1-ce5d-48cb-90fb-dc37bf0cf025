#include "imgui_Custom.h"

#include <chrono>
#include <unordered_map>

#include "XCJDrawList.h"

namespace fidenBoard {
    // Implement the Size constructor
    Size::Size(int width, int height) : width(width), height(height) {}

    // Implement InputText methods
    void InputText::setText(std::string val) { text = val; }
    std::string InputText::getText() { return text; }
    std::string* InputText::getAddress() { return &text; }
    InputText inputText;

    Properties::Properties(ImVec2 size, flags flag) : flag(flag), size(size.x, size.y) {}
    flags Properties::getFlags() { return flag; }
    void Properties::setFlags(flags flag) { this->flag = flag; }

    // Implement Lines methods
    Lines::Lines() {
        lines[0] = "qwertyuiop";
        lines[1] = "asdfghjkl";
        lines[2] = "zxcvbnm";
    }

    void Lines::toCaps(bool caps) {
        for (int i = 0; i < getSize(); i++) {
            std::string* str = &lines[i];
            for (int p = 0; p < lines[i].size(); p++) {
                char* r = &lines[i][p];
                if (caps) {
                    *r = toupper(*r);
                }
                else {
                    *r = tolower(*r);
                }
            }
        }
    }
    int Lines::getSize() { return sizeof lines / sizeof lines[0]; }
    Lines linez;
    namespace specialChars {
        bool isCaps = false; // Initialize isCaps

        void Canc::add(float width,float height) {
            if (ImGui::Button("删除", { width,height })) {
                std::string res;
                if (inputText.getText().size() == 0) { return; }
                for (int i = 0; i < (inputText.getText().size() - 1); i++) {
                    res += inputText.getText()[i];
                }
                *inputText.getAddress() = res;
            }
        }

        void Caps::add(float width,float height) {
            if (ImGui::Button("大写", { width,height})) {
                isCaps = !isCaps;
                for (int i = 0; i < linez.getSize(); i++) {
                    std::string* str = &linez.lines[i];
                    for (int p = 0; p < linez.lines[i].size(); p++) {
                        char* r = &linez.lines[i][p];
                        if (isCaps) {
                            *r = toupper(*r);
                        }
                        else {
                            *r = tolower(*r);
                        }
                    }
                }
            }
        }

        void Space::add() {
            if (ImGui::Button("空格", {ImGui::GetWindowWidth(), (ImGui::GetWindowHeight() / 10)})) {
                *inputText.getAddress() += ' ';
            }
        }
    }

    void FidenBoard::addKeyword(std::string carattere, float width, float height) {
        if (ImGui::Button(carattere.c_str(), {width, height})) {
            *inputText.getAddress() += carattere;
        }
        ImGui::SameLine();
    }
    void FidenBoard::addKeywordSameLine(std::string str, int width, int height) {
        for (int i = 0; i < str.size(); i++) {
            std::string charTostring(1, str[i]);
            addKeyword(charTostring, width,height);
        }
        ImGui::Text("");
    }
    std::string FidenBoard::getInputText() {
        return inputText.getText();
    }
    void FidenBoard::setInputText(std::string val) {
        inputText.setText(val);
    }
    FidenBoard::FidenBoard(ImVec2 size, flags flag) {
        properties = new Properties(size, flag);
        float startX = (ImGui::GetWindowWidth() - 10 * 100 - 20) * 0.5f;
        ImGui::SetCursorPosX(startX);
        addKeywordSameLine("1234567890");
        ImGui::SetCursorPosX(startX);
        addKeywordSameLine(linez.lines[0]);
        startX = (ImGui::GetWindowWidth() - 9 * 100 - 20) * 0.5f;
        ImGui::SetCursorPosX(startX);
        addKeywordSameLine(linez.lines[1]);
        startX = (ImGui::GetWindowWidth() - 10 * 100 + 15 - 20) * 0.5f;
        ImGui::SetCursorPosX(startX);
        caps.add(120);
        ImGui::SameLine();
        addKeywordSameLine(linez.lines[2]);
        ImGui::SameLine(9 * 100 + 20);
        canc.add(120);
    }
}

void DynamicIsland::addMessage(const std::string& content, float duration) {
    messages.emplace_back(content, duration);
}

void DynamicIsland::render(ImDrawList* drawList) {
    cleanExpiredMessages();
    applyAnimations();

    ImVec2 basePos(150, 50);
    ImVec2 baseSize(340, 80);

    ImU32 backgroundColor = IM_COL32(30, 30, 30, 255);
    ImU32 textColor = IM_COL32(255, 255, 255, 255);
    ImU32 shadowColor = IM_COL32(0, 0, 0, 128);

    for (size_t i = 0; i < messages.size(); ++i) {
        ImVec2 pos = ImVec2(basePos.x, basePos.y + i * baseSize.y + messages[i].bounceOffset);  // 增加回弹位移
        ImVec2 size = baseSize;

        float scale = animations[i];
        float elasticScale = easeOutElastic(scale);
        ImVec2 scaledPos = ImVec2(pos.x + (size.x * (1 - elasticScale) / 2), pos.y + (size.y * (1 - elasticScale) / 2));
        ImVec2 scaledSize = ImVec2(size.x * elasticScale, size.y * elasticScale);

        // 压缩和回弹动画处理
        ImVec2 compressionSize = ImVec2(size.x * scale, size.y);
        ImVec2 compressionPos = ImVec2(scaledPos.x + (size.x - compressionSize.x) / 2, scaledPos.y);

        // 阴影
        drawList->AddRectFilled(ImVec2(compressionPos.x + 5, compressionPos.y + 5), ImVec2(compressionPos.x + compressionSize.x + 5, compressionPos.y + scaledSize.y + 5), shadowColor, 20.0f);

        // 背景
        ImU32 animatedBackgroundColor = adjustAlpha(backgroundColor, messages[i].fadeOutProgress);
        drawList->AddRectFilled(compressionPos, ImVec2(compressionPos.x + compressionSize.x, compressionPos.y + scaledSize.y), animatedBackgroundColor, 20.0f);

        // 文本
        ImVec2 textPos = ImVec2(compressionPos.x + 15, compressionPos.y + 15);
        drawList->AddText(nullptr, 36.0f, textPos, adjustAlpha(textColor, messages[i].fadeOutProgress), messages[i].content.c_str());

        // 进度条
        if (!messages[i].persistent) {
            float timeLeft = messages[i].timeRemaining();
            float progress = std::max(timeLeft / messages[i].duration, 0.0f);
            ImVec2 progressBarPos = ImVec2(compressionPos.x + 15, compressionPos.y + scaledSize.y - 15);
            ImVec2 progressBarSize = ImVec2(compressionSize.x - 30, 6);
            drawList->AddRectFilled(progressBarPos, ImVec2(progressBarPos.x + progress * progressBarSize.x, progressBarPos.y + progressBarSize.y), IM_COL32(0, 150, 255, 255));
        }
    }
}

namespace NotificationSystem {

    enum class NotificationState {
        Closed,
        Opening,
        Open,
        Closing
    };

    struct Notification {
        NotificationState state = NotificationState::Closed;
        ImVec2 Pos {500, 500};
        float animationProgress = 0.0f;
        float displayTime = 2.0f;
        std::chrono::steady_clock::time_point startTime;
        std::string message;
        ImVec4 color {1.0f, 1.0f, 1.0f, 1.0f};  // 添加颜色控制

        Notification(const std::string& msg, float time, ImVec2 WindowPos)
            : message(msg), displayTime(time), Pos(WindowPos), state(NotificationState::Opening),
              startTime(std::chrono::steady_clock::now()) {
            animationProgress = 0.01f;
        }
    };

    // 改进EaseInOut函数，增强动画效果
    float EaseInOut(float t) {
        // 缓动曲线：弹出更快，收起更平滑
        return t < 0.5f ? 4.0f * t * t * t : 1.0f - std::pow(-2.0f * t + 2.0f, 3) / 2.0f;
    }

    struct NotificationManager::Impl {
        std::vector<Notification> notifications;

        void UpdateNotifications() {
            auto now = std::chrono::steady_clock::now();
            for (auto& notification : notifications) {
                UpdateNotification(notification, now);
            }
            notifications.erase(
                std::remove_if(notifications.begin(), notifications.end(), [](const Notification& n) {
                    return n.state == NotificationState::Closed;
                }),
                notifications.end()
            );
        }

        void ShowNotifications() {
            for (auto& notification : notifications) {
                if (notification.state != NotificationState::Closed) {
                    ShowNotification(notification);
                }
            }
        }

        void UpdateNotification(Notification& notification, const std::chrono::steady_clock::time_point& now) {
            float elapsedTime = std::chrono::duration_cast<std::chrono::duration<float>>(now - notification.startTime).count();
            switch (notification.state) {
                case NotificationState::Opening:
                    notification.animationProgress = elapsedTime * 0.5f;  // 控制弹出动画速度
                    if (notification.animationProgress >= 1.0f) {
                        notification.animationProgress = 1.0f;
                        notification.state = NotificationState::Open;
                        notification.startTime = now;
                    }
                    break;
                case NotificationState::Open:
                    if (elapsedTime >= notification.displayTime) {
                        notification.state = NotificationState::Closing;
                        notification.startTime = now;
                    }
                    break;
                case NotificationState::Closing:
                    notification.animationProgress = 1.0f - elapsedTime * 0.5f;  // 控制关闭动画速度
                    if (notification.animationProgress <= 0.0f) {
                        notification.animationProgress = 0.0f;
                        notification.state = NotificationState::Closed;
                    }
                    break;
                default:
                    break;
            }
        }

        void ShowNotification(Notification& notification) {
            float easedProgress = EaseInOut(notification.animationProgress);
            ImVec2 windowSize = ImVec2(300 * easedProgress, 100 * easedProgress);  // 动态改变窗口大小
            float windowAlpha = std::min(easedProgress, 1.0f);  // 透明度与动画进度同步

            // 设置窗口位置、大小、透明度等
            ImGui::SetNextWindowPos(notification.Pos);
            ImGui::SetNextWindowSize(windowSize);
            ImGui::SetNextWindowBgAlpha(windowAlpha);

            ImGui::Begin("提示", nullptr, ImGuiWindowFlags_NoDecoration | ImGuiWindowFlags_NoInputs);
            ImGui::Text("%s", notification.message.c_str());
            ImGui::End();
        }
    };

    NotificationManager::NotificationManager() : pImpl(new Impl()) {
        pImpl->notifications.reserve(10);
    }

    void NotificationManager::AddNotification(const std::string& message, float displayTime, ImVec2 Pos) {
        pImpl->notifications.emplace_back(message, displayTime, Pos);
    }

    void NotificationManager::UpdateAndRender() {
        pImpl->UpdateNotifications();
        pImpl->ShowNotifications();
    }
}

bool ImGui::M_SwitchButton(const char* str_id, bool* v, const ImVec2& size) {
    ImGuiWindow* window = GetCurrentWindow();
    if (window->SkipItems)
        return false;

    ImGuiContext& g = *GImGui;
    const ImGuiStyle& style = g.Style;
    const ImGuiID id = window->GetID(str_id);
    ImVec4* colors = GetStyle().Colors;
    ImVec2 pos = window->DC.CursorPos;
    ImDrawList* draw_list = GetWindowDrawList();

    const float gui_height = GetFrameHeight();
    const ImVec2 label_size = CalcTextSize(str_id, NULL, true);
    float width = gui_height * size.x;
    float height = gui_height * size.y;
    const ImRect total_bb(pos, ImVec2(pos.x + (width * 1.15f) + (label_size.x > 0.0f ? style.ItemInnerSpacing.x + label_size.x : 0.0f), pos.y + label_size.y + style.FramePadding.y * 2.0f));
    ItemSize(total_bb, style.FramePadding.y);
    if (!ItemAdd(total_bb, id))
        return false;

    static std::unordered_map<ImGuiID, float> anim_times;
    static std::unordered_map<ImGuiID, bool> button_states;
    const float anim_speed = 0.45f; // 控制动画速度的常量

    bool hovered, held;
    bool pressed = ButtonBehavior(total_bb, id, &hovered, &held);
    if (pressed) {
        *v = !(*v);
        MarkItemEdited(id);
        anim_times[id] = 0.0f; // 重置动画时间
        button_states[id] = *v; // 更新按钮状态
    }

    // 初始化动画时间和按钮状态
    if (anim_times.find(id) == anim_times.end()) {
        anim_times[id] = 0.0f; // 开始时没有延迟
        button_states[id] = *v;
    }

    anim_times[id] += g.IO.DeltaTime;
    anim_times[id] = ImMin(anim_times[id], anim_speed); // 限制动画时间

    float t = anim_times[id] / anim_speed;
    bool current_state = button_states[id];
    if (current_state)
        t = 1.0f - (1.0f - t) * (1.0f - t); // 使用缓出插值
    else
        t = t * t; // 使用缓入插值

    ImVec4 color_bg_from = current_state ? colors[ImGuiCol_ButtonActive] : colors[ImGuiCol_Button];
    ImVec4 color_bg_to = current_state ? colors[ImGuiCol_ButtonHovered] : colors[ImGuiCol_Button];
    ImVec4 color_bg = ImVec4(
        ImLerp(color_bg_from.x + 0.15f, color_bg_to.x + 0.15f, t),
        ImLerp(color_bg_from.y + 0.15f, color_bg_to.y + 0.15f, t),
        ImLerp(color_bg_from.z + 0.15f, color_bg_to.z + 0.15f, t),
        ImLerp(color_bg_from.w - 0.15f, color_bg_to.w - 0.15f, t)
    );

    ImVec4 color_circle_from = current_state ? ImVec4(1.0f, 1.0f, 1.0f, 1.0f) : colors[ImGuiCol_SliderGrab];
    ImVec4 color_circle_to = current_state ? colors[ImGuiCol_SliderGrab] : ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
    ImVec4 color_circle = ImVec4(
        ImLerp(color_circle_from.x, color_circle_to.x, t),
        ImLerp(color_circle_from.y, color_circle_to.y, t),
        ImLerp(color_circle_from.z, color_circle_to.z, t),
        ImLerp(color_circle_from.w, color_circle_to.w, t)
    );

    float circle_x = pos.x + (width * 0.15f) + t * (width * 0.69f); // 计算圆形位置
    if (!current_state) {
        circle_x = pos.x + (width * 0.85f) - t * (width * 0.69f); // 反向动画
    }

    if (height >= label_size.y) {
        float fg = (height - label_size.y) / 2;
        RenderText(ImVec2(pos.x + (width * 1.15f), pos.y + fg), str_id);
    }

    draw_list->AddRectFilled(pos, ImVec2(pos.x + width, pos.y + height), GetColorU32(color_bg), height * 0.80f);
    draw_list->AddCircleFilled(ImVec2(circle_x, pos.y + height * 0.50f), height * 0.435f, GetColorU32(color_circle));

    return *v;
}

bool ImGui::ButtonTextColored(const ImVec4& col, const char* fmt, ...) {
    va_list args;
    va_start(args, fmt);
    TextColoredV(col, fmt, args);
    va_end(args);
    return IsItemClicked();
}

bool ImGui::M_shut(const char* text) {
    const float w = GetWindowWidth() - 20;
    const ImVec2 text_size = CalcTextSize(text, NULL, true);
    SetCursorPosX(w - (text_size.x + GetFrameHeight()/4));
    if (SmallButton(text))
        return true;
    else 
        return false;
}

void ImGui::M_shut(const char* text, bool* close) {
    const float w = GetWindowWidth();
    const ImVec2 text_size = CalcTextSize(text, NULL, true);
    SetCursorPosX(w - (text_size.x + GetFrameHeight()/4));
    if (SmallButton(text))
       *close = true;
}

void ImGui::M_shut_G(const char* text, bool* close, const ImVec4& col) {
    const float w = GetWindowWidth();
    const ImVec2 text_size = CalcTextSize(text, NULL, true);
    SetCursorPosX(w - (text_size.x + GetFrameHeight()/4));
    if (ButtonTextColored(col, text)) {
       *close = !*close;
    }
}

void ImGui::RenderVerticalMenu(const std::vector<std::string>& menu_items, int& selected_index)
{
    const float slider_width = 200.0f;
    const float slider_height = 400.0f;
    const float item_height = slider_height / menu_items.size();

    ImVec4 slider_color = GetStyleColorVec4(ImGuiCol_SliderGrab);
    ImVec4 slider_bg_color = {slider_color.x, slider_color.y, slider_color.z, 0.1f};
    ImVec4 text_color = GetStyleColorVec4(ImGuiCol_Text);

    float target_position = selected_index * item_height;
    static float current_position = 0.0f;
    static float animation_progress = 0.0f;
    static bool animating = false;

    if (animating)
    {
        animation_progress += GetIO().DeltaTime * 5.0f; // 动画速度
        if (animation_progress >= 1.0f)
        {
            animation_progress = 1.0f;
            animating = false;
        }
    }
    current_position = (1.0f - animation_progress) * current_position + animation_progress * target_position;

    BeginGroup();

    // 绘制滑块
    ImDrawList* draw_list = GetWindowDrawList();
    ImVec2 menu_min = GetItemRectMin();
    ImVec2 menu_max = ImVec2(menu_min.x + slider_width, menu_min.y + item_height * menu_items.size());
    draw_list->AddRectFilled(menu_min, menu_max, ColorConvertFloat4ToU32(slider_bg_color), 15.0f);

    // 绘制滑块
    ImVec2 slider_min = ImVec2(menu_min.x, menu_min.y);
    ImVec2 slider_max = ImVec2(menu_min.x + slider_width, menu_min.y + slider_height);
    ImVec2 slider_pos_min = ImVec2(menu_min.x, slider_min.y + current_position);
    ImVec2 slider_pos_max = ImVec2(slider_max.x, slider_min.y + current_position + item_height);
    draw_list->AddRectFilled(slider_pos_min, slider_pos_max, ColorConvertFloat4ToU32(slider_color), 15.0f);

    // 绘制菜单项
    for (int i = 0; i < menu_items.size(); i++)
    {
        ImVec2 item_pos_min = ImVec2(menu_min.x, menu_min.y + i * item_height);

        // 绘制菜单项文本
        float text_x = item_pos_min.x + slider_width / 2 - CalcTextSize(menu_items[i].c_str()).x / 2.0f;
        float text_y = item_pos_min.y + (item_height / 2.0f) - 7.0f;
        draw_list->AddText(ImVec2(text_x, text_y), ColorConvertFloat4ToU32(text_color), menu_items[i].c_str());

        // 创建隐形按钮
        SetCursorScreenPos(item_pos_min);
        if (InvisibleButton(menu_items[i].c_str(), ImVec2(150, item_height)))
        {
            selected_index = i;
            animation_progress = 0.0f;
            animating = true;
        }
    }

    EndGroup();
}
