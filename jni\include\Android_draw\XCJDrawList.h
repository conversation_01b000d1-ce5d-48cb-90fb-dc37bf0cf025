//
// Created by s2049 on 24-7-14.
//

#ifndef XCJDRAWLIST_H
#define XCJDRAWLIST_H

#include "imgui.h"
#include "imgui_internal.h"
#include "VectorTools.h"

enum GradientDirection
{
    GradientHorizontal,
    GradientVertical
};

namespace XCJDrawList
{
    extern ImDrawList *thisDrawList;
    extern ImColor BoneDrawColor;

    void drawGradientRect(ImDrawList *drawList, const ImRect &rect, const ImColor &col1, const ImColor &col2, GradientDirection direction, float rounding = 0.0f, ImDrawFlags flags = 0);
    void drawWarningTriangle(const ImVec2 &pos, float size, ImU32 color);
    void drawBoldtext(float size, float x, float y, ImColor color, ImColor color1, const char *str);
    void drawBold(float size, int x, int y, ImVec4 color, const char *str);
    void drawTextY(const char *str, ImVec2 pos, int color, bool outline, float fontSize);
    void drawBone(ImVec2 start, ImVec2 end, bool Cansee);
    void offScreen(Vec2 Obj, float camear, ImColor color, float Radius);

}

#endif // XCJDRAWLIST_H
