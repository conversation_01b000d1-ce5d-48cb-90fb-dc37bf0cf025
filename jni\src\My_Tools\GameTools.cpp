//
// Created by s2049 on 24-7-9.
//

#include <cmath>

#include "ConfigManager.h"
#include "GameTools.h"
#include "Hack.h"
#include "KernelTools.h"
#include "imgui.h"

namespace GameOffset {
// 世界投类地址
uintptr_t UWorld_Offset;
uintptr_t UWorldLast_Offset; // PUBG专属
uintptr_t GNames_Offset;
uintptr_t GNamesLast_Offset; // PUBG专属
uintptr_t ULeve_Offset;
uintptr_t GMatrix_Offset;
uintptr_t GMatrix_Last_Offset; // PUBG专属
uintptr_t LineOfSightTo_Offset;

// 控制器类地址
uintptr_t Selfaddress_Offset;
uintptr_t UWorldToNectDriver_Offset;
uintptr_t NetDriverToServerConnection_Offset;
uintptr_t ServerConnectionToPlayerController_Offset;
uintptr_t PlayerControllerToMyselfActor_Offset;
uintptr_t ULeveToActors_Offset;
uintptr_t ULeveToActorsCount_Offset;
uintptr_t STExtraPlayerController_Offset;

// 对象属性类地址
uintptr_t PlayerTeam_Offset;
uintptr_t PlayerCameraManager_Offset;
uintptr_t WeaponManagerComponent_Offset;
uintptr_t CurrentWeaponReplicated_Offset;
uintptr_t CurrentUsingWeaponSafety_Offset;
uintptr_t WeaponEntityComp_Offset;
uintptr_t ShootWeaponType_Offset;
uintptr_t CameraCache_Offset;
uintptr_t CameraPOV_Offset;
uintptr_t CameraLocation_Offset;
uintptr_t CameraLocationLocalSpace_Offset;
uintptr_t CameraRotation_Offset;
uintptr_t CameraFOV_Offset;
uintptr_t ControlRotation_Offset;
uintptr_t RootComponent_Offset;
uintptr_t PlayertWorldPos_Offset;
uintptr_t IsFiring_Offset;
uintptr_t PlayerHight_Offset;
uintptr_t State_Offset;
uintptr_t Health_Offset;
uintptr_t bDead_Offset;
uintptr_t IsBot_Offset;
uintptr_t PlayerName_Offset;
uintptr_t BoneMesh_Offset;
uintptr_t BoneHuman_Offset;
uintptr_t BonePtr_Offset;

int NowGameSet;
} // namespace GameOffset

/*

掩体    0xA0538A4
gapp  0x10B5F498
270矩阵：0x1122C960
Gworld：0x11259798
Gname：0x10BE9988
Guobject：0x110088B8
750320矩阵 0x112313B0
*/

bool GameOffset::InitToGameOffset(int GameSet) {
  NowGameSet = GameSet;
  switch (GameSet) {
  case ChinaPUBG: {
    // 世界投类地址
    UWorld_Offset = 0x12161358;
    GNames_Offset = 0x11EFDCA0;
    ULeve_Offset = 0x90;
    GMatrix_Offset = 0x12132D60;
    LineOfSightTo_Offset = 0xA0538A4;

    // 控制器类地址
    UWorldToNectDriver_Offset = 0x98;
    NetDriverToServerConnection_Offset = 0x88;
    ServerConnectionToPlayerController_Offset = 0x30;
    PlayerControllerToMyselfActor_Offset = 0x32a8;
    ULeveToActors_Offset = 0xA0;
    ULeveToActorsCount_Offset = 0xA8;
    STExtraPlayerController_Offset = 0x5588;
    PlayerCameraManager_Offset = 0x608;

    // 对象属性类地址
    PlayerTeam_Offset =
        0xac0; // UAECharacter.Character.Pawn.Actor.Object #TeamID
    CurrentUsingWeaponSafety_Offset =
        0xfe8;                             // STExtraWeapon* CurrentUsingWeaponSafety;
    ShootWeaponType_Offset = 0xbd0;        // 	int RepWeaponID
    CameraCache_Offset = 0x5f0;            // CameraCacheEntry CameraCache
    CameraPOV_Offset = 0x10;               // 不变
    CameraLocation_Offset = 0x0;           // 不变
    CameraLocationLocalSpace_Offset = 0xc; // 不变
    CameraRotation_Offset = 0x18;          // 不变
    CameraFOV_Offset = 0x30;               // 不变
    ControlRotation_Offset = 0x5a8;        // 准心指针
    RootComponent_Offset = 0x278;          // RootComponent
    PlayertWorldPos_Offset = 0x200;        // Transform ParticleToAddOffset;/
    IsFiring_Offset = 0x23e0;              // bIsWeaponFiring
    PlayerHight_Offset = 0x3518;           // HighWalkSpeed
    State_Offset = 0x1538;                 // PawnStateRepSyncData
    Health_Offset = 0xed8;                 // Health
    bDead_Offset = 0xf58;                  // HealthMax
    IsBot_Offset = 0xadc;
    PlayerName_Offset = 0xa40;
    BoneMesh_Offset = 0x600;
    BoneHuman_Offset = 0x1F0;
    BonePtr_Offset = 0x7F8;

    break;
  };
  case PUBG: {
    // 世界投类地址
    UWorld_Offset = 0xCF14F60; //+0x20
    UWorldLast_Offset = 0x20;
    GNames_Offset = 0xCCA7020; //+0x110
    GNamesLast_Offset = 0x110;
    ULeve_Offset = 0x20;
    GMatrix_Offset = 0xD3E30A0;
    GMatrix_Last_Offset = 0xD3E9A70;
    LineOfSightTo_Offset = 0x896C034;

    // 控制器类地址
    Selfaddress_Offset = 0xD46DD68;
    UWorldToNectDriver_Offset = 0x8;
    NetDriverToServerConnection_Offset = 0x48;
    ServerConnectionToPlayerController_Offset = 0x20;
    ULeveToActors_Offset = 0xA0;
    ULeveToActorsCount_Offset = 0xA8;
    STExtraPlayerController_Offset = 0x4090;
    PlayerCameraManager_Offset = 0x4D0;

    // 对象属性类地址
    PlayerTeam_Offset = 0x938;
    WeaponManagerComponent_Offset = 0x2328;
    CurrentWeaponReplicated_Offset = 0x500;
    WeaponEntityComp_Offset = 0x840;
    ShootWeaponType_Offset = 0x178;
    CameraCache_Offset = 0x4B0; // Class: PlayerCameraManager.Actor.Object 下的
                                // CameraCacheEntry CameraCache;
    CameraPOV_Offset =
        0x10;                    // Class: CameraCacheEntry 下的 MinimalViewInfo POV;
    CameraLocation_Offset = 0x0; // Class: MinimalViewInfo 下的 Vector Location;
    CameraLocationLocalSpace_Offset =
        0xc; // Class: MinimalViewInfo 下的 Vector LocationLocalSpace;
    CameraRotation_Offset =
        0x18;                // Class: MinimalViewInfo 下的 Rotator Rotation;
    CameraFOV_Offset = 0x24; // Class: MinimalViewInfo 下的 float FOV;
    ControlRotation_Offset = 0x468;
    RootComponent_Offset = 0x1B0;
    PlayertWorldPos_Offset = 0x1C0;
    IsFiring_Offset = 0x1640;
    PlayerHight_Offset = 0x2868;
    State_Offset = 0xF88;
    Health_Offset = 0xDC0;
    bDead_Offset = 0xDDC;
    IsBot_Offset = 0x9E9;
    PlayerName_Offset = 0x8F0;
    BoneMesh_Offset = 0x498;
    BoneHuman_Offset = 0x1B0;
    BonePtr_Offset = 0x878;

    break;
  };
  default: {

    break;
  };
  }
  return true;
}

namespace GameTools {
short bone_getcount;
bool IsStartRead = false;
bool IsGetCodePtr = false;
Vec2 HalfscreenSize;
unordered_map<int, string> ClassCache;
unordered_map<string, string> GrenadeClassNameMap;
vector<MaterialsInto> MaterialsIntoVector;
vector<smokeObject> smokeObjects;
uintptr_t Gname_address, Bone_address;
uintptr_t infdata, CodePtr;
FTransform meshtrans, headtrans;
FMatrix c2wMatrix, boneMatrix;
FMatrix worldMatrix;
} // namespace GameTools

uint64_t GameTools::DecryptActorsArray(uint64_t uLevel, int Actors_Offset, int EncryptedActors_Offset) {
  if (uLevel < 0x10000000)
    return 0;

  if (driver::read<uint64_t>(uLevel + Actors_Offset) > 0)
    return uLevel + Actors_Offset;

  if (driver::read<uint64_t>(uLevel + EncryptedActors_Offset) > 0)
    return uLevel + EncryptedActors_Offset;

  auto AActors = driver::read<Actors>(uLevel + EncryptedActors_Offset + 0x10);

  if (AActors.Enc_1 > 0) {
    auto Enc = driver::read<Chunk>(AActors.Enc_1 + 0x80);
    return (((driver::read<uint8_t>(AActors.Enc_1 + Enc.val_1) |
              (driver::read<uint8_t>(AActors.Enc_1 + Enc.val_2) << 8)) |
             (driver::read<uint8_t>(AActors.Enc_1 + Enc.val_3) << 0x10)) &
                0xFFFFFF |
            ((uint64_t)driver::read<uint8_t>(AActors.Enc_1 + Enc.val_4)
             << 0x18) |
            ((uint64_t)driver::read<uint8_t>(AActors.Enc_1 + Enc.val_5)
             << 0x20)) &
               0xFFFF00FFFFFFFFFF |
           ((uint64_t)driver::read<uint8_t>(AActors.Enc_1 + Enc.val_6)
            << 0x28) |
           ((uint64_t)driver::read<uint8_t>(AActors.Enc_1 + Enc.val_7)
            << 0x30) |
           ((uint64_t)driver::read<uint8_t>(AActors.Enc_1 + Enc.val_8) << 0x38);
  } else if (AActors.Enc_2 > 0) {
    auto Lost_Actors = driver::read<uint64_t>(AActors.Enc_2);
    if (Lost_Actors > 0) {
      return (uint16_t)(Lost_Actors - 0x400) & 0xFF00 |
             (uint8_t)(Lost_Actors - 0x04) |
             (Lost_Actors + 0xFC0000) & 0xFF0000 |
             (Lost_Actors - 0x4000000) & 0xFF000000 |
             (Lost_Actors + 0xFC00000000) & 0xFF00000000 |
             (Lost_Actors + 0xFC0000000000) & 0xFF0000000000 |
             (Lost_Actors + 0xFC000000000000) & 0xFF000000000000 |
             (Lost_Actors - 0x400000000000000) & 0xFF00000000000000;
    }
  } else if (AActors.Enc_3 > 0) {
    auto Lost_Actors = driver::read<uint64_t>(AActors.Enc_3);
    if (Lost_Actors > 0) {
      return (Lost_Actors >> 0x38) | (Lost_Actors << (64 - 0x38));
    }
  } else if (AActors.Enc_4 > 0) {
    auto Lost_Actors = driver::read<uint64_t>(AActors.Enc_4);
    if (Lost_Actors > 0) {
      return Lost_Actors ^ 0xCDCD00;
    }
  }
  return 0;
}

float GameTools::GetDistance(const Vec2 &self, const Vec2 &other) {
  float dx = pow(self.x - other.x, 2);
  float dy = pow(self.y - other.y, 2);
  return sqrt(dx + dy);
}

void GameTools::GetDistance(Vec3 Object, Vec3 Self, float *Distance) {
  float DistanceX = pow(Object.x - Self.x, 2);
  float DistanceY = pow(Object.y - Self.y, 2);
  float DistanceZ = pow(Object.z - Self.z, 2);
  *Distance = sqrt(DistanceX + DistanceY + DistanceZ) * 0.01f;
}

FMatrix GameTools::rotatorToMatrix(Rotator rotation) {
  float radPitch = rotation.Pitch * ((float)M_PI / 180.0f);
  float radYaw = rotation.Yaw * ((float)M_PI / 180.0f);
  float radRoll = rotation.Roll * ((float)M_PI / 180.0f);

  float SP = sinf(radPitch);
  float CP = cosf(radPitch);
  float SY = sinf(radYaw);
  float CY = cosf(radYaw);
  float SR = sinf(radRoll);
  float CR = cosf(radRoll);

  FMatrix matrix;

  matrix[0][0] = (CP * CY);
  matrix[0][1] = (CP * SY);
  matrix[0][2] = (SP);
  matrix[0][3] = 0;

  matrix[1][0] = (SR * SP * CY - CR * SY);
  matrix[1][1] = (SR * SP * SY + CR * CY);
  matrix[1][2] = (-SR * CP);
  matrix[1][3] = 0;

  matrix[2][0] = (-(CR * SP * CY + SR * SY));
  matrix[2][1] = (CY * SR - CR * SP * SY);
  matrix[2][2] = (CR * CP);
  matrix[2][3] = 0;

  matrix[3][0] = 0;
  matrix[3][1] = 0;
  matrix[3][2] = 0;
  matrix[3][3] = 1;

  return matrix;
}

// 使用POV矩阵进行的坐标转换函数
void GameTools::WorldToScreen(Vec2 *bscreen, Vec3 *obj) {
  Vec3 vAxisX(worldMatrix[0][0], worldMatrix[0][1], worldMatrix[0][2]);
  Vec3 vAxisY(worldMatrix[1][0], worldMatrix[1][1], worldMatrix[1][2]);
  Vec3 vAxisZ(worldMatrix[2][0], worldMatrix[2][1], worldMatrix[2][2]);
  Vec3 vDelta = *obj - MainUpData.SelfViewInfo.Location;
  Vec3 vTransformed(Vec3::Dot(vDelta, vAxisY), Vec3::Dot(vDelta, vAxisZ), Vec3::Dot(vDelta, vAxisX));
  if (vTransformed.z < 1.0f) {
    vTransformed.z = 1.0f;
  }
  bscreen->x = (HalfscreenSize.x +
                vTransformed.x *
                    (HalfscreenSize.x / tanf(MainUpData.SelfViewInfo.FOV *
                                             ((float)M_PI / 360.0f))) /
                    vTransformed.z);
  bscreen->y = (HalfscreenSize.y -
                vTransformed.y *
                    (HalfscreenSize.x / tanf(MainUpData.SelfViewInfo.FOV *
                                             ((float)M_PI / 360.0f))) /
                    vTransformed.z);
}

void GameTools::WorldToScreen(Vec2 *bscreen, Vec3 obj) {
  Vec3 vAxisX(worldMatrix[0][0], worldMatrix[0][1], worldMatrix[0][2]);
  Vec3 vAxisY(worldMatrix[1][0], worldMatrix[1][1], worldMatrix[1][2]);
  Vec3 vAxisZ(worldMatrix[2][0], worldMatrix[2][1], worldMatrix[2][2]);
  Vec3 vDelta = obj - MainUpData.SelfViewInfo.Location;
  Vec3 vTransformed(Vec3::Dot(vDelta, vAxisY), Vec3::Dot(vDelta, vAxisZ), Vec3::Dot(vDelta, vAxisX));
  if (vTransformed.z < 1.0f) {
    vTransformed.z = 1.0f;
  }
  bscreen->x = (HalfscreenSize.x +
                vTransformed.x *
                    (HalfscreenSize.x / tanf(MainUpData.SelfViewInfo.FOV *
                                             ((float)M_PI / 360.0f))) /
                    vTransformed.z);
  bscreen->y = (HalfscreenSize.y -
                vTransformed.y *
                    (HalfscreenSize.x / tanf(MainUpData.SelfViewInfo.FOV *
                                             ((float)M_PI / 360.0f))) /
                    vTransformed.z);
}

Vec2 GameTools::WorldToScreen(Vec3 obj) {
  Vec2 bscreen;
  Vec3 vAxisX(worldMatrix[0][0], worldMatrix[0][1], worldMatrix[0][2]);
  Vec3 vAxisY(worldMatrix[1][0], worldMatrix[1][1], worldMatrix[1][2]);
  Vec3 vAxisZ(worldMatrix[2][0], worldMatrix[2][1], worldMatrix[2][2]);
  Vec3 vDelta = obj - MainUpData.SelfViewInfo.Location;
  Vec3 vTransformed(Vec3::Dot(vDelta, vAxisY), Vec3::Dot(vDelta, vAxisZ), Vec3::Dot(vDelta, vAxisX));
  if (vTransformed.z < 1.0f) {
    vTransformed.z = 1.0f;
  }
  bscreen.x = (HalfscreenSize.x +
               vTransformed.x *
                   (HalfscreenSize.x / tanf(MainUpData.SelfViewInfo.FOV *
                                            ((float)M_PI / 360.0f))) /
                   vTransformed.z);
  bscreen.y = (HalfscreenSize.y -
               vTransformed.y *
                   (HalfscreenSize.x / tanf(MainUpData.SelfViewInfo.FOV *
                                            ((float)M_PI / 360.0f))) /
                   vTransformed.z);
  return bscreen;
}

string GameTools::GetFromFName(int index) {
  if (ClassCache.find(index) == ClassCache.end()) {
    char Name[32] = "";
    uintptr_t ye =
        driver::read<uintptr_t>(Gname_address + (index / 0x4000) * 0x8);
    uintptr_t xu = driver::read<uintptr_t>(ye + (index % 0x4000) * 0x8);
    driver::read(xu + 0xC, &Name, 32);

    ClassCache[index] = Name;
  }

  return ClassCache[index];
}

char TempUTF8[32];
char *GameTools::getPlayerName(long namepy) {
  UTF16 buf16[16] = {0};
  driver::read(namepy, buf16, 28);
  UTF16 *pTempUTF16 = buf16;
  UTF8 *pTempUTF8 = TempUTF8;
  ;
  UTF8 *pUTF8End = pTempUTF8 + 32;
  while (pTempUTF16 < pTempUTF16 + 28) {
    if (*pTempUTF16 <= 0x007F && pTempUTF8 + 1 < pUTF8End) {
      *pTempUTF8++ = static_cast<UTF8>(*pTempUTF16);
    } else if (*pTempUTF16 >= 0x0080 && *pTempUTF16 <= 0x07FF &&
               pTempUTF8 + 2 < pUTF8End) {
      *pTempUTF8++ = (*pTempUTF16 >> 6) | 0xC0;
      *pTempUTF8++ = (*pTempUTF16 & 0x3F) | 0x80;
    } else if (*pTempUTF16 >= 0x0800 && *pTempUTF16 <= 0xFFFF &&
               pTempUTF8 + 3 < pUTF8End) {
      *pTempUTF8++ = (*pTempUTF16 >> 12) | 0xE0;
      *pTempUTF8++ = ((*pTempUTF16 >> 6) & 0x3F) | 0x80;
      *pTempUTF8++ = (*pTempUTF16 & 0x3F) | 0x80;
    } else {
      break;
    }
    pTempUTF16++;
  }

  return TempUTF8;
}

Vec3 GameTools::MarixToVector(FMatrix matrix) {
  return Vec3(matrix.M[3][0], matrix.M[3][1], matrix.M[3][2]);
}

FMatrix GameTools::MatrixMulti(FMatrix m1, FMatrix m2) {
  FMatrix matrix = FMatrix();
  for (int i = 0; i < 4; i++) {
    for (int j = 0; j < 4; j++) {
      for (int k = 0; k < 4; k++) {
        matrix.M[i][j] += m1.M[i][k] * m2.M[k][j];
      }
    }
  }
  return matrix;
}

FTransform GameTools::getBone(uintptr_t addr) {
  FTransform transform;
  driver::read(addr, &transform.Rotation, sizeof(Quat));
  driver::read(addr + sizeof(Quat), &transform.Translation, sizeof(Vec3));
  driver::read(addr + sizeof(Quat) + sizeof(Vec3), &transform.chunk, sizeof(float));
  driver::read(addr + sizeof(Quat) + sizeof(Vec3) + sizeof(float),
               &transform.Scale3D,
               sizeof(Vec3));
  return transform;
}

FMatrix GameTools::TransformToMatrix(FTransform transform) {
  FMatrix matrix;
  matrix.M[3][0] = transform.Translation.x;
  matrix.M[3][1] = transform.Translation.y;
  matrix.M[3][2] = transform.Translation.z;
  float x2 = transform.Rotation.x + transform.Rotation.x;
  float y2 = transform.Rotation.y + transform.Rotation.y;
  float z2 = transform.Rotation.z + transform.Rotation.z;
  float xx2 = transform.Rotation.x * x2;
  float yy2 = transform.Rotation.y * y2;
  float zz2 = transform.Rotation.z * z2;
  matrix.M[0][0] = (1 - (yy2 + zz2)) * transform.Scale3D.x;
  matrix.M[1][1] = (1 - (xx2 + zz2)) * transform.Scale3D.y;
  matrix.M[2][2] = (1 - (xx2 + yy2)) * transform.Scale3D.z;
  float yz2 = transform.Rotation.y * z2;
  float wx2 = transform.Rotation.w * x2;
  matrix.M[2][1] = (yz2 - wx2) * transform.Scale3D.z;
  matrix.M[1][2] = (yz2 + wx2) * transform.Scale3D.y;
  float xy2 = transform.Rotation.x * y2;
  float wz2 = transform.Rotation.w * z2;
  matrix.M[1][0] = (xy2 - wz2) * transform.Scale3D.y;
  matrix.M[0][1] = (xy2 + wz2) * transform.Scale3D.x;
  float xz2 = transform.Rotation.x * z2;
  float wy2 = transform.Rotation.w * y2;
  matrix.M[2][0] = (xz2 + wy2) * transform.Scale3D.z;
  matrix.M[0][2] = (xz2 - wy2) * transform.Scale3D.x;
  matrix.M[0][3] = 0;
  matrix.M[1][3] = 0;
  matrix.M[2][3] = 0;
  matrix.M[3][3] = 1;
  return matrix;
}

bool GameTools::LineOfSightTo(BoneStruct Poin) {
  if (ConfigManager::Settings.isNotAimSmoke) {
    if (!smokeObjects.empty()) {
      for (auto smoke : smokeObjects) {
        float distance;
        GetDistance(Poin.Pos, MainUpData.SelfCoordinate, &distance);

        if (distance < smoke.screenDistance) {
          return true;
        }

        Vec2 minScreenPos = smoke.screenPos[0];
        Vec2 maxScreenPos = smoke.screenPos[0];

        for (int i = 1; i < 8; ++i) {
          minScreenPos.x = min(minScreenPos.x, smoke.screenPos[i].x);
          minScreenPos.y = min(minScreenPos.y, smoke.screenPos[i].y);
          maxScreenPos.x = max(maxScreenPos.x, smoke.screenPos[i].x);
          maxScreenPos.y = max(maxScreenPos.y, smoke.screenPos[i].y);
        }

        if (Poin.ScreenPos.x >= minScreenPos.x &&
            Poin.ScreenPos.x <= maxScreenPos.x &&
            Poin.ScreenPos.y >= minScreenPos.y &&
            Poin.ScreenPos.y <= maxScreenPos.y) {
          return false;
        }
      }
    }
  }
  if (!IsGetCodePtr)
    return true;
  driver::write(infdata + 0x20 + sizeof(BoneStruct) * bone_getcount, &Poin, sizeof(BoneStruct));
  return true;
}

bool GameTools::GetBoneInit(uintptr_t Human, uintptr_t _Bone_address) {
  meshtrans = getBone(Human);
  c2wMatrix = TransformToMatrix(meshtrans);
  Bone_address = _Bone_address;
  bone_getcount = 0;

  return true;
}

bool GameTools::GetBoneData(int BoneNumber, BoneStruct *tBone) {
  headtrans = getBone(Bone_address + BoneNumber);
  boneMatrix = TransformToMatrix(headtrans);
  tBone->Pos = MarixToVector(MatrixMulti(boneMatrix, c2wMatrix));
  tBone->ScreenPos = WorldToScreen(tBone->Pos);
  tBone->CanSee = LineOfSightTo(*tBone);
  bone_getcount++;

  return true;
}

bool GameTools::GetBoneData(int BoneNumber, BoneStruct *tBone, int PosZ) {
  headtrans = getBone(Bone_address + BoneNumber);
  boneMatrix = TransformToMatrix(headtrans);
  tBone->Pos = MarixToVector(MatrixMulti(boneMatrix, c2wMatrix));
  tBone->Pos.z += PosZ;
  tBone->ScreenPos = WorldToScreen(tBone->Pos);
  tBone->CanSee = LineOfSightTo(*tBone);
  bone_getcount++;

  return true;
}

Rotator GameTools::toRotator(Vec3 local, Vec3 target) {
  Vec3 rotation = local - target;
  Rotator newViewAngle = {0, 0, 0};
  float hyp = sqrt(rotation.x * rotation.x + rotation.y * rotation.y);
  newViewAngle.Pitch = -atan(rotation.z / hyp) * (180.0 / (double)M_PI);
  newViewAngle.Yaw = atan(rotation.y / rotation.x) * (180.0 / (double)M_PI);
  newViewAngle.Roll = (float)0.f;

  if (rotation.x >= 0.f)
    newViewAngle.Yaw += 180.0f;
  return newViewAngle;
}

Vec3 GameTools::calculateBulletImpact(const Rotator &angle, float distance) {
  float pitchRad = angle.Pitch * (M_PI / 180.0f);
  float yawRad = angle.Yaw * (M_PI / 180.0f);

  Vec3 direction;
  direction.x = cos(pitchRad) * cos(yawRad);
  direction.y = cos(pitchRad) * sin(yawRad);
  direction.z = sin(pitchRad);

  // Scale the direction vector by the distance
  direction.x *= distance;
  direction.y *= distance;
  direction.z *= distance;

  return direction;
}

float GameTools::getMinAngle(float Target, float self) {
  float d1, d2;
  d1 = Target - self;
  d2 = 360.0 - fabs(d1);
  if (d1 > 0) {
    d2 = -d2;
  }
  return fabs(d1) < fabs(d2) ? d1 : d2;
}

Vec2 GameTools::rotateCoord(float angle, float objRadar_x, float objRadar_y) {
  Vec2 radarCoordinate;
  float s = sin(angle * M_PI / 180);
  float c = cos(angle * M_PI / 180);
  radarCoordinate.x = objRadar_x * c + objRadar_y * s;
  radarCoordinate.y = -objRadar_x * s + objRadar_y * c;
  return radarCoordinate;
}

Vec2 GameTools::rotatePoint(Vec2 center, float radius, float angleDegree) {
  Vec2 point;
  angleDegree = angleDegree * 3.1415926 / 180;
  point.x = center.x + radius * cos(angleDegree);
  point.y = center.y + radius * sin(angleDegree);
  return point;
}
