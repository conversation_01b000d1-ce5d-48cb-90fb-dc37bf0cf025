#include <cstring>
#include <dirent.h>
#include <fcntl.h>
#include <fstream>
#include <future>
#include <iostream>
#include <linux/input.h>
#include <poll.h>
#include <regex>
#include <sstream>
#include <sys/epoll.h>
#include <unistd.h>

#include "AimBot.h"
#include "AndroidImgui.h"
#include "ConfigManager.h"
#include "GraphicsManager.h"
#include "HttpClient.h"
#include "KernelTools.h"
#include "draw.h"
#include "driver.h"
#include "dynamic_Island.h"
#include "timer.h"
// by 奶龙 QQ3213525537

using namespace std;

Timer MainThreadTimer;
touch touchdriven;
Driver touchDriver; // 内核触摸驱动对象

RESOLUTION_INFORMATION resolution_information;
TOUCH_INFORMATION touch_information;
bool is_only_draw = false;

int main(int argc, char *argv[]) {
  bool isFinish = false;

  if (argc == 3) {
    is_set_screen = true;
    try {
      screen_x_set = atoi(argv[2]);
      screen_y_set = atoi(argv[3]);
    } catch (std::exception &e) {
      cout << "参数错误" << endl;
      return -1;
    }
  }

  // 初始化
  cout << "初始化成功" << endl;
  ConfigManager::ConfigManager_Init();
  ConfigManager::Settings = ConfigManager::GetDefaultConfig();
  ConfigManager::loadMapConfig();

  // 获取图形接口
  ::graphics = GraphicsManager::getGraphicsInterface(GraphicsManager::OPENGL);

  // 获取屏幕信息
  ::screen_config();

  // 获取屏幕分辨率
  ::native_window_screen_x = (::displayInfo.height > ::displayInfo.width ? ::displayInfo.height : ::displayInfo.width);
  ::native_window_screen_y = (::displayInfo.height > ::displayInfo.width ? ::displayInfo.height : ::displayInfo.width);
  ::abs_ScreenX = (::displayInfo.height > ::displayInfo.width ? ::displayInfo.height : ::displayInfo.width);
  ::abs_ScreenY = (::displayInfo.height < ::displayInfo.width ? ::displayInfo.height : ::displayInfo.width);
  resolution_information.FixedScreenWidth = displayInfo.width;
  resolution_information.FixedScreenHeiht = displayInfo.height;

  ::window = android::ANativeWindowCreator::Create("androidwindow_gui", native_window_screen_x, native_window_screen_y, permeate_record);
  graphics->Init_Render(::window, native_window_screen_x, native_window_screen_y);

  // 初始化触摸
  touch_information.TouchPoints.y = ConfigManager::Settings.TouchPosy;
  touch_information.TouchPoints.x = ConfigManager::Settings.TouchPosx;
  touch_information.TouchRadius = ConfigManager::Settings.TouchRange;
  touch_information.floatswitch[0] = ConfigManager::Settings.TouchFPS;
  touch_information.floatswitch[1] = ConfigManager::Settings.Speed_X;
  touch_information.floatswitch[2] = ConfigManager::Settings.Speed_Y;
  touch_information.floatswitch[3] = ConfigManager::Settings.Speed;
  touch_information.Accuracy_X = ConfigManager::Settings.accuracy_X;
  touch_information.Accuracy_Y = ConfigManager::Settings.accuracy_Y;

  // 初始化内核层触摸驱动
  if (!touchDriver.uinput_init(displayInfo.width, displayInfo.height)) {
    cout << "内核触摸驱动初始化失败!" << endl;
    return -1;
  }
  cout << "内核触摸驱动初始化成功!" << endl;

  // 初始化触摸线程 (改为使用内核层触摸)
  // new thread(&HandleTouchEvent);
  new thread(&HandleImGuiTouchEvent);  // 启动ImGui触摸事件处理线程
  new thread(&HideVolumeKeys, &isFinish);
  ::init_My_drawdata(); // 初始化绘制数据


  // 初始化UI
  static bool flag = true;
  MainThreadTimer.SetFps(ConfigManager::Settings.RunFPS);
  MainThreadTimer.AotuFPS_init();
  notificationManager = new DynamicIsland();
  while (flag) {
    drawBegin();
    graphics->NewFrame();
    Layout_tick_UI(&flag);
    DrawESP(ImGui::GetForegroundDrawList());
    notificationManager->render(ImGui::GetBackgroundDrawList());
    dynamic_Island_XCJ::SetIslandSize(ConfigManager::Settings.IslandSize);
    dynamic_Island_XCJ::SetIslandPos(ImVec2(screen_x / 2, 20));
    dynamic_Island_XCJ::Render(ImGui::GetBackgroundDrawList());
    if (!is_only_draw) {
      // 处理触摸逻辑（原来在HandleTouchEvent线程中）
      touchdriven.GetTouch(&touch_information, &resolution_information, Vec2(displayInfo.width, displayInfo.height));
      AimBotStart();
    }
    graphics->EndFrame();
    MainThreadTimer.AotuFPS();
  }

  delete notificationManager;
  graphics->Shutdown();
  android::ANativeWindowCreator::Destroy(::window);

  return 0;
}
