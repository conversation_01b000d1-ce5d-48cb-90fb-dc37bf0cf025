//
// Created by s2049 on 24-7-14.
//

#include "XCJDrawList.h"

#include <array>

#include "ConfigManager.h"
#include "draw.h"

namespace XCJDrawList {
    ImDrawList *thisDrawList;
    ImColor BoneDrawColor;
}

// 绘制渐变色矩形的自定义函数
void XCJDrawList::drawGradientRect(ImDrawList *drawList, const ImRect& rect, const ImColor& col1, const ImColor& col2, GradientDirection direction, float rounding, ImDrawFlags flags) {
    // 获取矩形的四个顶点
    ImVec2 p0 = rect.Min;
    ImVec2 p1 = ImVec2(rect.Max.x, rect.Min.y);
    ImVec2 p2 = rect.Max;
    ImVec2 p3 = ImVec2(rect.Min.x, rect.Max.y);

    // 创建顶点数据
    struct Vert {
        ImVec2 pos;
        ImColor col;
    };
    Vert verts[4];

    if (direction == GradientHorizontal) {
        // 水平渐变
        verts[0] = { p0, col1 };
        verts[1] = { p1, col2 };
        verts[2] = { p2, col2 };
        verts[3] = { p3, col1 };
    } else if (direction == GradientVertical) {
        // 垂直渐变
        verts[0] = { p0, col1 };
        verts[1] = { p1, col1 };
        verts[2] = { p2, col2 };
        verts[3] = { p3, col2 };
    }

    // 创建索引数据
    const ImU16 idxs[6] = {
        0, 1, 2,
        0, 2, 3
    };

    // 绘制矩形
    drawList->PrimReserve(6, 4);

    // 写入顶点
    for (int i = 0; i < 4; i++) {
        drawList->PrimWriteVtx(verts[i].pos, ImVec2(0, 0), verts[i].col);
    }

    // 写入索引
    for (int i = 0; i < 6; i++) {
        drawList->PrimWriteIdx(idxs[i]);
    }
}

void XCJDrawList::drawWarningTriangle(const ImVec2& pos, float size, ImU32 color) {
    ImVec2 p1 = pos;
    ImVec2 p2 = ImVec2(pos.x + size, pos.y);
    ImVec2 p3 = ImVec2(pos.x + size / 2, pos.y - size * 0.866f);

    thisDrawList->AddTriangle(p1, p2, p3, color, 2.0f);
    thisDrawList->AddTriangleFilled(p1, p2, p3, IM_COL32(255, 255, 0, 255));

    // 绘制感叹号
    ImVec2 exclamation_mark_pos = ImVec2(pos.x + size / 2, pos.y - size * 0.433f);
    thisDrawList->AddLine(ImVec2(exclamation_mark_pos.x - 2.0f, exclamation_mark_pos.y), ImVec2(exclamation_mark_pos.x + 2.0f, exclamation_mark_pos.y), IM_COL32(0, 0, 0, 255), 2.0f);
    thisDrawList->AddLine(ImVec2(exclamation_mark_pos.x, exclamation_mark_pos.y - 6.0f), ImVec2(exclamation_mark_pos.x, exclamation_mark_pos.y), IM_COL32(0, 0, 0, 255), 2.0f);
}

void XCJDrawList::drawBoldtext(float size, float x, float y, ImColor color, ImColor color1, const char* str)
{
    thisDrawList->AddText(NULL, size, ImVec2(x - 0.1, y - 0.1), color1, str);
    thisDrawList->AddText(NULL, size, ImVec2(x + 0.1, y + 0.1), color1, str);
    thisDrawList->AddText(NULL, size, ImVec2(x, y), color, str);
}

void XCJDrawList::drawBold(float size,int x, int y, ImVec4 color, const char* str)
{
    thisDrawList->AddText(NULL, size,ImVec2(x + 1, y), ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)), str);
    thisDrawList->AddText(NULL, size,ImVec2(x - 0.1, y), ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)), str);
    thisDrawList->AddText(NULL, size,ImVec2(x, y + 1), ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)), str);
    thisDrawList->AddText(NULL, size,ImVec2(x, y - 1), ImGui::ColorConvertFloat4ToU32(ImVec4(0.0f, 0.0f, 0.0f, 1.0f)), str);
    thisDrawList->AddText(NULL, size,ImVec2(x, y), ImGui::ColorConvertFloat4ToU32(color), str);
}

void XCJDrawList::drawTextY(const char* str, ImVec2 pos, int color, bool outline, float fontSize)
{
    ImVec2 vec2 = pos;

    if (outline) {
        ImU32 outlineColor = 0xFF000000;
        thisDrawList->AddText(NULL, fontSize, ImVec2(vec2.x + 1, vec2.y + 1), outlineColor, str);
        thisDrawList->AddText(NULL, fontSize, ImVec2(vec2.x - 1, vec2.y - 1), outlineColor, str);
        thisDrawList->AddText(NULL, fontSize, ImVec2(vec2.x + 1, vec2.y - 1), outlineColor, str);
        thisDrawList->AddText(NULL, fontSize, ImVec2(vec2.x - 1, vec2.y + 1), outlineColor, str);
    }

    thisDrawList->AddText(NULL, fontSize, vec2, color, str);
}

void XCJDrawList::drawBone(ImVec2 start, ImVec2 end, bool Cansee)
{
    if (Cansee)
        thisDrawList->AddLine(start, end, BoneDrawColor, {ConfigManager::Settings.BoneSize});
    else
        thisDrawList->AddLine(start, end, ImColor(0, 255, 0), {ConfigManager::Settings.BoneSize});
}

void XCJDrawList::offScreen(Vec2 Obj, float angle, ImColor color, float Radius)
{
    angle = angle * (M_PI / 180.0);
    ImRect screen_rect = {0.0f, 0.0f, static_cast<float>(screen_x), static_cast<float>(screen_y)};
    auto screen_center = screen_rect.GetCenter();
    Vec2 arrow_center {
        screen_center.x + Radius * cosf(angle),
        screen_center.y + Radius * sinf(angle)
    };
    std::array<ImVec2, 4>points {
        ImVec2(-22.0f, -8.6f),
        ImVec2(0.0f, 0.0f),
        ImVec2(-22.0f, 8.6f),
        ImVec2(-18.0f, 0.0f)
};
    for (auto & point : points)
    {
        auto x = point.x * 1.155f;
        auto y = point.y * 1.155f;
        point.x = arrow_center.x + x * cosf(angle) - y * sinf(angle);
        point.y = arrow_center.y + x * sinf(angle) + y * cosf(angle);
    }
    float alpha = 1.0f;
    ImColor arrowColor = color;
    arrowColor.Value.w = std::min(alpha, 1.0f);
    ImGui::GetBackgroundDrawList()->AddTriangleFilled(points[0], points[1], points[3], arrowColor);
    ImGui::GetBackgroundDrawList()->AddTriangleFilled(points[2], points[1], points[3], arrowColor);
    ImGui::GetBackgroundDrawList()->AddQuad(points[0], points[1], points[2], points[3], ImColor(0.0f, 0.0f, 0.0f, alpha), 1.335f);
}