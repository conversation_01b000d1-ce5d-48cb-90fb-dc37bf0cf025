﻿#ifndef HTTPTOOLS_H
#define HTTPTOOLS_H

#include <string>
#include <memory>

namespace HttpHelper {
    static std::string httpGet(const std::string& hostname, const std::string& url);
    static std::string httpPost(const std::string& hostname, const std::string& url, const std::string& data);
    static std::string getIp(const std::string& hostname);
    static int hexToInt(const std::string& hex);
    static std::string strstrstr(const std::string& str, const std::string& front, const std::string& rear);
};

#endif //HTTPTOOLS_H