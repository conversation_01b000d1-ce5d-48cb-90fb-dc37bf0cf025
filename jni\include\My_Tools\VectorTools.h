//
// Created by s2049 on 24-7-10.
//

#ifndef VECTORTOOLS_H
#define VECTORTOOLS_H

#include <string>
#include <vector>

#include "imgui.h"

using namespace std;

struct Vec2 {
  float x;
  float y;
  Vec2() {
    this->x = 0;
    this->y = 0;
  }
  Vec2(float x, float y) {
    this->x = x;
    this->y = y;
  }
  Vec2 operator+(float v) const {
    return Vec2(x + v, y + v);
  }
  Vec2 operator-(float v) const {
    return Vec2(x - v, y - v);
  }
  Vec2 operator*(float v) const {
    return Vec2(x * v, y * v);
  }
  Vec2 operator/(float v) const {
    return Vec2(x / v, y / v);
  }
  Vec2 &operator+=(float v) {
    x += v;
    y += v;
    return *this;
  }
  Vec2 &operator-=(float v) {
    x -= v;
    y -= v;
    return *this;
  }
  Vec2 &operator*=(float v) {
    x *= v;
    y *= v;
    return *this;
  }
  Vec2 &operator/=(float v) {
    x /= v;
    y /= v;
    return *this;
  }
  Vec2 operator+(const Vec2 &v) const {
    return Vec2(x + v.x, y + v.y);
  }
  Vec2 operator-(const Vec2 &v) const {
    return Vec2(x - v.x, y - v.y);
  }
  Vec2 operator*(const Vec2 &v) const {
    return Vec2(x * v.x, y * v.y);
  }
  Vec2 operator/(const Vec2 &v) const {
    return Vec2(x / v.x, y / v.y);
  }
  Vec2 &operator+=(const Vec2 &v) {
    x += v.x;
    y += v.y;
    return *this;
  }
  Vec2 &operator-=(const Vec2 &v) {
    x -= v.x;
    y -= v.y;
    return *this;
  }
  Vec2 &operator*=(const Vec2 &v) {
    x *= v.x;
    y *= v.y;
    return *this;
  }
  Vec2 &operator/=(const Vec2 &v) {
    x /= v.x;
    y /= v.y;
    return *this;
  }
};

struct Vec3 {
  float x;
  float y;
  float z;

  Vec3() {
    x = y = z = 0.0f;
  }

  Vec3(float _x, float _y, float _z) {
    x = _x;
    y = _y;
    z = _z;
  }

  Vec3 operator+(const Vec3 &v) const {
    return {x + v.x, y + v.y, z + v.z};
  }

  Vec3 operator-(const Vec3 &v) const {
    return {x - v.x, y - v.y, z - v.z};
  }

  bool operator==(const Vec3 &v) {
    return x == v.x && y == v.y && z == v.z;
  }

  bool operator!=(const Vec3 &v) {
    return !(x == v.x && y == v.y && z == v.z);
  }

  static Vec3 Zero() {
    return {0.0f, 0.0f, 0.0f};
  }

  static float Dot(Vec3 lhs, Vec3 rhs) {
    return (((lhs.x * rhs.x) + (lhs.y * rhs.y)) + (lhs.z * rhs.z));
  }

  static float Distance(Vec3 a, Vec3 b) {
    Vec3 vector = Vec3(a.x - b.x, a.y - b.y, a.z - b.z);
    //  return sqrt(((vector.x * vector.x) + (vector.y * vector.y)) + (vector.z * vector.z));
  }
};

struct Vec4 {
  float x;
  float y;
  float z;
  float w;
  Vec4() {
    this->x = 0;
    this->y = 0;
    this->z = 0;
    this->w = 0;
  }
  Vec4(float x, float y, float z, float w) {
    this->x = x;
    this->y = y;
    this->z = z;
    this->w = w;
  }
};

struct Rotator {
  float Pitch;
  float Yaw;
  float Roll;
  Rotator() {}
  Rotator(float _P, float _Y, float _R) :
      Pitch(_P), Yaw(_Y), Roll(_R) {}
};

struct FMatrix {
  float M[4][4];
  float *operator[](int index) {
    return M[index]; // 重载 [] 运算符，使得可以像数组一样访问矩阵的每一行
  }
};

struct Quat {
  float x;
  float y;
  float z;
  float w;
};

struct MinimalViewInfo {
  Vec3 Location;           // 相机位置
  Vec3 LocationLocalSpace; //  本地空间
  Rotator Rotation;        // 相机旋转信息
  float FOV;               // 视场角 (Field of View)
};

struct FTransform {
  Quat Rotation;
  Vec3 Translation;
  float chunk;
  Vec3 Scale3D;
};

struct BoneStruct {
  Vec3 Pos;           // 世界坐标
  Vec2 ScreenPos;     // 屏幕坐标
  bool CanSee = true; // 可见判断
};

struct PlayerBone {
  BoneStruct Head;
  BoneStruct Chest;
  BoneStruct Pelvis;
  BoneStruct Left_Shoulder;
  BoneStruct Right_Shoulder;
  BoneStruct Left_Elbow;
  BoneStruct Right_Elbow;
  BoneStruct Left_Wrist;
  BoneStruct Right_Wrist;
  BoneStruct Left_Thigh;
  BoneStruct Right_Thigh;
  BoneStruct Left_Knee;
  BoneStruct Right_Knee;
  BoneStruct Left_Ankle;
  BoneStruct Right_Ankle;
};

struct Into_Box {
  string Name;   // 物资名称
  ImColor color; // 绘制颜色
  int BoxNum;
};

struct IntoList {
  float Time;
  uintptr_t address;
  float width;    // 物资宽度
  Vec3 Pos;       // 世界坐标
  Vec2 ScreenPos; // 屏幕坐标
  float Distance; // 距离
  string Name;    // 资源名称
  ImColor color;  // 绘制颜色
  int Type;       // 资源类型
};

struct PlayerList {
  Vec3 Pos;          // 世界坐标
  Vec2 ScreenPos;    // 屏幕坐标
  float width;       // 玩家宽度
  int TeamID;        // 队标
  int State;         // 动作
  bool IsBot;        // 人机
  float Health;      // 血量百分比
  float Distance;    // 距离
  string PlayerName; // 玩家名称
  float Angle;       // 偏航角
  int WeaponID;      // 手持id
  Vec3 VelocitySafety;
  bool CanSee = true;
  int BoneCount;
  BoneStruct LockBone;
  PlayerBone player_bone;
};

struct UpPlayer {
  uintptr_t address;
  uintptr_t coordptr_address;
  string PlayerName;
  Vec3 objPos;
  bool IsRobot;
  int State;
  int Team;
};

struct UpData {
  int Team;
  bool IsFiring;
  bool IsAiming;
  int SelfState;
  int SelfWeaponType;
  Vec3 SelfCoordinate;
  float bulletSpeed;
  MinimalViewInfo SelfViewInfo;
  Rotator SelfControlRotation;

  uintptr_t UWorld_address;
  uintptr_t GNames_address;
  uintptr_t GMatrix_Offset;
  uintptr_t ObjectActors_address;
  uintptr_t SelfActors_address;
  uintptr_t SelfCameraManager_address;
  uintptr_t SelfController_address;
  uintptr_t SelfSTExtraController_address;
  vector<UpPlayer> ObjectActors;
};

struct smokeObject {
  float screenDistance;
  Vec3 worldPos;
  Vec2 screenPos[8];
};

typedef struct _TOUCH_INFORMATION {
  float Scal;
  float TouchRadius;
  Vec2 TouchPoints;
  float floatswitch[6];
  bool TouchAimAtControl;
  Vec2 MouseCoordinate;
  Vec2 AimingCoordinates;
  bool TouchOrientationControl;
  float Accuracy_X;
  float Accuracy_Y;
} TOUCH_INFORMATION, *PTOUCH_INFORMATION;

typedef struct _RESOLUTION_INFORMATION {
  int Orientation;
  int ScreenWidth;
  int ScreenHeiht;
  int FixedScreenWidth;
  int FixedScreenHeiht;
} RESOLUTION_INFORMATION, *PRESOLUTION_INFORMATION;

namespace VectorTools {
/**
 * 比较两个三维向量是否相等。
 *
 * @param VectorFirst 第一个向量。
 * @param VectorLast 第二个向量。
 * @return 是否相等，相等返回true，不相等返回false
 */
bool CompareVector3(Vec3 VectorFirst, Vec3 VectorLast);

/**
 * 比较两个二维向量是否相等。
 *
 * @param VectorFirst 第一个向量。
 * @param VectorLast 第二个向量。
 * @return 是否相等，相等返回true，不相等返回false
 */
bool CompareVector2(Vec2 *VectorFirst, Vec2 *VectorLast);
} // namespace VectorTools

#endif // VECTORTOOLS_H
