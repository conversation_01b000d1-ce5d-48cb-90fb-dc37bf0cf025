//
// Created by s2049 on 7/2/2024.
//

#include <fcntl.h>
#include <string>
#include <sys/types.h>
#include <unistd.h>
#include <unordered_map>
#include <vector>

#include "AimBot.h"
#include "ConfigManager.h"

#include <fstream>
#include <sstream>

#include "GameTools.h"

namespace ConfigManager
{
  Config Settings;                       // 当前加载的配置
  unordered_map<string, Config> configs; // 存储配置名和配置对象的映射
  string defaultConfigName;              // 默认配置的名称
  string currentConfigName;              // 当前配置的名称
  int configsCount;                      // 配置数量
  int WeaponMapCount;                    // 武器配置数量
} // namespace ConfigManager

bool ConfigManager::loadMapConfig()
{
  ifstream file("/data/system/XCJ_wzpz.cfg");
  if (!file.is_open())
  {
    return false;
  }

  string line;
  int count = 0;
  while (getline(file, line))
  {
    if (line.empty())
    {
      continue;
    }

    // 处理#对象类型
    if (line[0] == '#')
    {
      MaterialsInto temp;

      stringstream ss(line.substr(1));
      ss >> temp.name >> temp.color.Value.x >> temp.color.Value.y >>
          temp.color.Value.z >> temp.color.Value.w;

      GameTools::MaterialsIntoVector.push_back(temp);
      count++;
      continue;
    }

    size_t pipePos = line.find('|');
    size_t spacePos = line.find(' ', pipePos);

    if (pipePos == string::npos || spacePos == string::npos)
    {
      continue;
    }

    GameTools::MaterialsIntoVector[count - 1].Map[line.substr(0, pipePos)] =
        line.substr(pipePos + 1, spacePos - pipePos - 1);
    GameTools::MaterialsIntoVector[count - 1].MapBOOL[line.substr(0, pipePos)] =
        (line.substr(spacePos + 1) == "true");
  }

  file.close();

  return true;
}

bool ConfigManager::saveMapConfig()
{
  ofstream file("/data/system/XCJ_wzpz.cfg");
  if (!file.is_open())
  {
    return false;
  }

  // 遍历MaterialsIntoVector，将数据写入文件
  for (const auto &material : GameTools::MaterialsIntoVector)
  {
    // 写入#对象类型及其颜色信息
    file << "#" << material.name << " " << material.color.Value.x
         << " " // 假设ImVec4的成员是x, y, z, w
         << material.color.Value.y << " " << material.color.Value.z << " "
         << material.color.Value.w << std::endl;

    // 写入Map和MapBOOL信息
    for (const auto &entry : material.Map)
    {
      const auto &key = entry.first;
      const auto &value = entry.second;
      bool boolValue = material.MapBOOL.at(key); // 获取MapBOOL的值

      file << key << "|" << value << " " << (boolValue ? "true" : "false")
           << std::endl;
    }
  }

  file.close();

  return true;
}

// 保存函数
bool ConfigManager::SaveConfigManager()
{
  nlohmann::json j;

  // 保存配置数量
  j["configsCount"] = configs.size();

  // 保存默认配置名称
  j["defaultConfigName"] = defaultConfigName;

  // 保存每个配置
  for (const auto &pair : configs)
  {
    pair.second.to_json(j["configs"][pair.first]);
  }

  // 保存武器地图
  int mapcount = 0;
  for (auto map : WeaponMap)
  {
    j["WeaponMap"][mapcount] = map.first;
    j["WeaponMapValue"][mapcount] = map.second;
    mapcount++;
  }

  // 将JSON写入文件
  std::ofstream file("/data/system/xcjguiNew_config_JSON");
  if (!file.is_open())
  {
    perror("Error opening file");
    return false;
  }

  file << std::setw(4) << j << std::endl;
  file.close();
  return true;
}

// 加载函数
bool ConfigManager::LoadConfigManager()
{
  std::ifstream file("/data/system/xcjguiNew_config_JSON");
  if (!file.is_open())
  {
    perror("Error opening file");
    return false;
  }

  nlohmann::json j;
  file >> j;
  file.close();

  // 加载配置数量
  int configsCount = j.value("configsCount", 0);
  if (configsCount > 99)
  {
    system("su rm -f /data/xcjguiNew_config");
    return false;
  }

  // 加载默认配置名称
  defaultConfigName = j.value("defaultConfigName", "");

  // 加载每个配置
  for (const auto &pair : j["configs"].items())
  {
    Config config;
    try
    {
      config.from_json(pair.value());
    }
    catch (const std::exception &e)
    {
      perror("Error parsing config");
      continue;
    }
    configs[pair.key()] = config;
  }

  // 加载武器地图
  int count = 0;
  vector<int> temp_key;
  for (const auto &map : j["WeaponMap"].items())
  {
    temp_key.push_back(map.value());
    count++;
  }

  for (int i = 0; i < count; i++)
  {
    WeaponMap[temp_key[i]] = j["WeaponMapValue"][i];
  }

  return true;
}

bool ConfigManager::SaveConfig(const string &name, const Config &config)
{
  // 检查配置是否存在
  if (configs.find(name) != configs.end())
  {
    configs[name] = config;
    SaveConfigManager();
    return true;
  }
  return false;
}

// 添加新配置
bool ConfigManager::AddConfig(const string &name, const Config &config)
{
  // 检查配置是否已存在
  if (configs.find(name) != configs.end())
  {
    return false; // 配置已存在，无法添加
  }
  configs[name] = config;
  configsCount++;
  SaveConfigManager();
  return true;
}

// 移除配置
bool ConfigManager::RemoveConfig(const string &name)
{
  // 检查配置是否存在
  if (configs.erase(name) > 0)
  {
    configsCount--;
    SaveConfigManager();
    return true;
  }
  else
  {
    return false;
  }
}

// 获取配置
Config ConfigManager::GetConfig(const string &name)
{
  auto it = configs.find(name);
  if (it != configs.end())
  {
    return it->second;
  }
  return {};
}

// 设置默认配置
bool ConfigManager::SetDefaultConfig(const string &name)
{
  if (configs.find(name) != configs.end())
  {
    defaultConfigName = name;
    SaveConfigManager();
    return true;
  }
  return false;
}

// 获取默认配置
Config ConfigManager::GetDefaultConfig()
{
  return GetConfig(defaultConfigName);
}

// 切换当前配置
bool ConfigManager::SetCurrentConfig(const string &name)
{
  if (configs.find(name) != configs.end())
  {
    currentConfigName = name;
    return true;
  }
  return false;
}

// 获取当前配置
Config ConfigManager::GetCurrentConfig()
{
  return GetConfig(currentConfigName);
}

// 获取配置数量
int ConfigManager::GetConfigCount() { return configsCount; }

// 恢复为默认配置
void ConfigManager::ResetToDefaultConfig()
{
  currentConfigName = defaultConfigName;
}

// 获取当前配置名称
string ConfigManager::GetCurrentConfigName() { return currentConfigName; }

// 获取默认配置名称
string ConfigManager::GetDefaultConfigName() { return defaultConfigName; }

// 获取配置名称列表
vector<string> ConfigManager::GetConfigNames()
{
  vector<string> names;
  for (const auto &pair : configs)
  {
    names.push_back(pair.first);
  }
  return names;
}

void ConfigManager::ConfigManager_Init()
{
  configsCount = 0;
  if (!LoadConfigManager())
  {
    defaultConfigName = "default";
    currentConfigName = "default";
    Config defaultConfig;
    WeaponMap[101008] = 1.2;
    WeaponMap[101102] = 0.7;
    WeaponMap[101003] = 0.6;
    WeaponMap[101001] = 0.8;
    WeaponMap[102105] = 0.5;
    WeaponMap[101004] = 0.6;
    WeaponMap[101002] = 0.8;
    WeaponMap[101009] = 1.05;
    WeaponMap[101010] = 1.15;
    WeaponMap[101011] = 1;
    WeaponMap[101012] = 0.9;
    WeaponMap[102001] = 0.6;
    WeaponMap[105001] = 1.3;
    WeaponMap[101006] = 0.7;
    WeaponMap[101007] = 0.85;
    WeaponMap[105002] = 1.2;
    WeaponMap[100103] = 0.65;
    WeaponMap[102003] = 0.6;
    WeaponMap[105010] = 0.45;
    WeaponMap[102002] = 0.55;
    WeaponMap[102007] = 0.6;
    WeaponMap[102004] = 0.6;
    WeaponMap[101005] = 1.05;
    WeaponMap[103100] = 1;
    WeaponMap[103006] = 1;
    WeaponMap[102005] = 0.6;
    WeaponMap[104102] = 1;
    WeaponMap[103002] = 1;
    WeaponMapCount = WeaponMap.size();
    AddConfig(defaultConfigName, defaultConfig);
  }
  else
  {
    SetCurrentConfig(defaultConfigName);
  }
}
