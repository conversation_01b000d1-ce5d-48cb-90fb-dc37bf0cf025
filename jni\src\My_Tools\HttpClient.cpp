﻿// HttpHelper.cpp
#include "HttpClient.h"
#include <arpa/inet.h>
#include <cstring>
#include <iostream>
#include <netdb.h>
#include <sys/select.h>
#include <sys/socket.h>
#include <unistd.h>

std::string HttpHelper::httpGet(const std::string &hostname,
                                const std::string &url)
{
  int sockfd;
  struct sockaddr_in serveraddr;
  socklen_t addrlen = sizeof(serveraddr);

  if ((sockfd = socket(AF_INET, SOCK_STREAM, 0)) < 0)
  {
    return "";
  }

  memset(&serveraddr, 0, addrlen);
  serveraddr.sin_family = AF_INET;
  serveraddr.sin_port = htons(80);

  struct hostent *host = gethostbyname(hostname.c_str());
  if (host == NULL)
  {
    close(sockfd);
    return "";
  }

  struct in_addr ip = *((struct in_addr *)host->h_addr);
  serveraddr.sin_addr = ip;

  if (connect(sockfd, (struct sockaddr *)&serveraddr, addrlen) < 0)
  {
    close(sockfd);
    return "";
  }

  char request[2048];
  snprintf(request, sizeof(request),
           "GET /%s HTTP/1.1\r\n"
           "Host: %s\r\n"
           "Accept:*/*\r\n"
           "Accept-Encoding:deflate, br\r\n"
           "Content-Type: application/x-www-form-urlencoded\r\n\r\n",
           url.c_str(), hostname.c_str());

  if (send(sockfd, request, strlen(request), 0) == -1)
  {
    close(sockfd);
    return "";
  }

  fd_set fds;
  struct timeval tv = {3, 0};
  FD_ZERO(&fds);
  FD_SET(sockfd, &fds);

  if (select(sockfd + 1, &fds, NULL, NULL, &tv) < 1)
  {
    close(sockfd);
    return "";
  }

  if (FD_ISSET(sockfd, &fds))
  {
    char buffer[1024];
    char *ptr = buffer;
    char *end = buffer + 1023;
    int readlen;
    while ((readlen = read(sockfd, ptr, 1)) > 0)
    {
      if (*ptr == '\n')
      {
        if (strncmp(ptr - 3, "\r\n\r", 3) == 0)
        {
          *++ptr = '\0';
          break;
        }
      }
      ptr++;
    }

    if (!readlen)
    {
      close(sockfd);
      return "";
    }

    std::string response(buffer);
    size_t pos = response.find("\r\n\r\n");
    if (pos != std::string::npos)
    {
      response = response.substr(pos + 4);
    }

    close(sockfd);
    return response;
  }

  close(sockfd);
  return "";
}

std::string HttpHelper::httpPost(const std::string &hostname,
                                 const std::string &url,
                                 const std::string &data)
{
  int sockfd;
  struct sockaddr_in serveraddr;
  socklen_t addrlen = sizeof(serveraddr);

  if ((sockfd = socket(AF_INET, SOCK_STREAM, 0)) < 0)
  {
    return "";
  }

  memset(&serveraddr, 0, addrlen);
  serveraddr.sin_family = AF_INET;
  serveraddr.sin_port = htons(80);

  struct hostent *host = gethostbyname(hostname.c_str());
  if (host == NULL)
  {
    close(sockfd);
    return "";
  }

  struct in_addr ip = *((struct in_addr *)host->h_addr);
  serveraddr.sin_addr = ip;

  if (connect(sockfd, (struct sockaddr *)&serveraddr, addrlen) < 0)
  {
    close(sockfd);
    return "";
  }

  char request[2048];
  snprintf(request, sizeof(request),
           "POST /%s HTTP/1.1\r\n"
           "Host: %s\r\n"
           "Content-Type: application/x-www-form-urlencoded\r\n"
           "Accept:*/*\r\n"
           "Accept-Encoding:deflate, br\r\n"
           "Content-Length: %zu\r\n\r\n"
           "%s\r\n",
           url.c_str(), hostname.c_str(), data.size(), data.c_str());

  if (send(sockfd, request, strlen(request), 0) == -1)
  {
    close(sockfd);
    return "";
  }

  fd_set fds;
  struct timeval tv = {3, 0};
  FD_ZERO(&fds);
  FD_SET(sockfd, &fds);

  if (select(sockfd + 1, &fds, NULL, NULL, &tv) < 1)
  {
    close(sockfd);
    return "";
  }

  if (FD_ISSET(sockfd, &fds))
  {
    char buffer[1024];
    char *ptr = buffer;
    char *end = buffer + 1023;
    int readlen;
    while ((readlen = read(sockfd, ptr, 1)) > 0)
    {
      if (*ptr == '\n')
      {
        if (strncmp(ptr - 3, "\r\n\r", 3) == 0)
        {
          *++ptr = '\0';
          break;
        }
      }
      ptr++;
    }

    if (!readlen)
    {
      close(sockfd);
      return "";
    }

    std::string response(buffer);
    size_t pos = response.find("\r\n\r\n");
    if (pos != std::string::npos)
    {
      response = response.substr(pos + 4);
    }

    close(sockfd);
    return response;
  }

  close(sockfd);
  return "";
}

std::string HttpHelper::getIp(const std::string &hostname)
{
  struct hostent *host = gethostbyname(hostname.c_str());
  if (host == NULL)
  {
    return "";
  }
  struct in_addr ip = *((struct in_addr *)host->h_addr);
  return inet_ntoa(ip);
}

int HttpHelper::hexToInt(const std::string &hex)
{
  int value = 0;
  for (char ch : hex)
  {
    if (ch >= 'A' && ch <= 'F')
      value = (ch - 55) + 16 * value;
    else if (ch >= 'a' && ch <= 'f')
      value = (ch - 87) + 16 * value;
    else if (ch >= '0' && ch <= '9')
      value = (ch - 48) + 16 * value;
    else
      return value;
  }
  return value;
}

std::string HttpHelper::strstrstr(const std::string &str,
                                  const std::string &front,
                                  const std::string &rear)
{
  size_t pos = str.find(front);
  if (pos == std::string::npos)
    return "";

  size_t start = pos + front.size();
  pos = str.find(rear, start);
  if (pos == std::string::npos)
    return "";

  return str.substr(start, pos - start);
}
