//
// Created by s2049 on 24-7-14.
// Modified to use KMA driver only
//

#include "KernelTools.h"
#include "draw.h"
#include "driver.h"

namespace driver {

// KMA驱动实例
Driver *kma_driver = nullptr;
pid_t current_pid = 0;
bool is_initialized = false;

} // namespace driver

bool driver::read(uintptr_t addr, void *buffer, size_t size) {
  if (kma_driver && is_initialized) {
    return kma_driver->read(addr, buffer, size);
  }
  return false;
}

bool driver::write(uintptr_t addr, void *buffer, size_t size) {
  if (kma_driver && is_initialized) {
    return kma_driver->write(addr, buffer, size);
  }
  return false;
}

uintptr_t driver::get_module_base(char *name, pid_t pid) {
  if (kma_driver && is_initialized) {
    return kma_driver->get_module_base(pid, name);
  }
  return 0;
}

bool driver::initialkernel(pid_t gamepid) {
  current_pid = gamepid;

  // 初始化KMA驱动
  try {
    if (!kma_driver) {
      kma_driver = new Driver();
    }

    // 设置CPU亲和性，使用小核心
    kma_driver->cpuset(0, 4);

    // 初始化pid
    if (kma_driver->initpid(gamepid)) {
      is_initialized = true;
      notificationManager->addMessage("驱动识别成功 by KMA", 1);
      printf("[+] KMA驱动识别成功\n");
      return true;
    } else {
      printf("[-] KMA驱动初始化失败\n");
      delete kma_driver;
      kma_driver = nullptr;
      return false;
    }
  } catch (...) {
    printf("[-] KMA驱动加载失败\n");
    if (kma_driver) {
      delete kma_driver;
      kma_driver = nullptr;
    }
    return false;
  }
}

// KMA驱动专用函数
bool driver::read_safe(uintptr_t addr, void *buffer, size_t size) {
  if (kma_driver && is_initialized) {
    return kma_driver->read_safe(addr, buffer, size);
  }
  return false;
}

pid_t driver::get_pid(char *name) {
  if (kma_driver) {
    return kma_driver->get_pid(name);
  }
  return -1;
}

pid_t driver::get_pid(char *name, char *comm) {
  if (kma_driver) {
    return kma_driver->get_pid(name, comm);
  }
  return -1;
}

uintptr_t driver::get_module_base(pid_t pid, char *name, size_t size) {
  if (kma_driver && is_initialized) {
    return kma_driver->get_module_base(pid, name, size);
  }
  return 0;
}

void driver::cpuset(int num) {
  if (kma_driver) {
    kma_driver->cpuset(num);
  }
}

void driver::cpuset(int start, int end) {
  if (kma_driver) {
    kma_driver->cpuset(start, end);
  }
}

// 清理资源
void driver::cleanup() {
  if (kma_driver) {
    delete kma_driver;
    kma_driver = nullptr;
  }
  is_initialized = false;
}