#ifndef XCJ_TOUCH_H
#define XCJ_TOUCH_H

#include <sys/mman.h>
#include <dirent.h>
#include <unistd.h>
#include <cmath>
#include <thread>
#include "timer.h"
#include "VectorTools.h"

#define BITS_PER_LONG (sizeof(long) * 8)
#define test_bit(array, bit) ((array[bit / BITS_PER_LONG] >> bit % BITS_PER_LONG) & 1)
#define NBITS(x) ((((x) - 1) / BITS_PER_LONG) + 1)

enum FingerStatus {
    FINGER_NO, // 无状态
    FINGER_X_UPDATE, // X更新
    FINGER_Y_UPDATE, // Y更新
    FINGER_XY_UPDATE, // XY同时更新
    FINGER_UP // 抬起
};

struct TouchFinger {
    int x = -1, y = -1; // 触摸点XY数据
    int tracking_id = -1; // 触摸点追踪ID数据
    int status = FINGER_NO;
    timeval time;
};

extern bool GrabTouchScreen();

extern void HandleTouchEvent();

extern void HandleImGuiTouchEvent();

extern void Touch_Down(int slot, int x, int y);

extern void Touch_Move(int slot, int x,int y);

extern void Touch_Up(int slot);

struct BotTouch {
    Vec2 endLU;
    Vec2 endRD;
    Vec2 mspeed;
    Vec2 maxMove;
    Vec2 AimMove;
    Vec2 Touchsize;
    Vec2 nowPointer;
    long long loadTime;
    bool botio = false;
    Vec2 beginPointer;
    bool botstart = false;
    float ThreadSpeed = 0;
    Vec2 AimRot = {0, 0};
    Vec2 *TouchProportion;
    TOUCH_INFORMATION* touch_information = nullptr;
    struct timespec strtime, endtime, Intervaltime;
    RESOLUTION_INFORMATION* resolution_information = nullptr;

    BotTouch(TOUCH_INFORMATION* _touch_information, RESOLUTION_INFORMATION* _resolution_information, Vec2 Touchsize_) {
    	if (_touch_information != nullptr && _resolution_information != nullptr) {
    		touch_information = _touch_information;
    		resolution_information = _resolution_information;
    	}
    	clock_gettime(CLOCK_MONOTONIC, &strtime);
        Touchsize = Touchsize_;
        beginPointer.x = Touchsize.x * touch_information->TouchPoints.x;
        beginPointer.y = Touchsize.y * touch_information->TouchPoints.y;
        endLU.x = beginPointer.x - Touchsize.x * (touch_information->TouchRadius);
        endLU.y = beginPointer.y - Touchsize.y * (touch_information->TouchRadius);
        endRD.x = beginPointer.x + Touchsize.x * (touch_information->TouchRadius);
        endRD.y = beginPointer.y + Touchsize.y * (touch_information->TouchRadius);
        maxMove.x = (Touchsize.x * (touch_information->TouchRadius) / (touch_information->floatswitch[0] * 0.2));
        maxMove.y = (Touchsize.y * (touch_information->TouchRadius) / (touch_information->floatswitch[0] * 0.2));
        ThreadSpeed = 2000 / touch_information->floatswitch[0];
        botio = false;
    }

    void setTouch() {
        if (resolution_information->Orientation == 1) {
            beginPointer.x = Touchsize.x * touch_information->TouchPoints.x;
            beginPointer.y = Touchsize.y * touch_information->TouchPoints.y;
            endLU.x = beginPointer.x - Touchsize.x * (touch_information->TouchRadius);
            endLU.y = beginPointer.y - Touchsize.y * (touch_information->TouchRadius);
            endRD.x = beginPointer.x + Touchsize.x * (touch_information->TouchRadius);
            endRD.y = beginPointer.y + Touchsize.y * (touch_information->TouchRadius);
            maxMove.x = (Touchsize.x * touch_information->floatswitch[3] / touch_information->floatswitch[0]);
            maxMove.y = (Touchsize.y * touch_information->floatswitch[3] / touch_information->floatswitch[0]);
            ThreadSpeed = 2000 / touch_information->floatswitch[0];
        } else if (resolution_information->Orientation == 3) {
            beginPointer.x = Touchsize.x * (1 - touch_information->TouchPoints.x);
            beginPointer.y = Touchsize.y * (1 - touch_information->TouchPoints.y);
            endLU.x = beginPointer.x - Touchsize.x * (touch_information->TouchRadius);
            endLU.y = beginPointer.y - Touchsize.y * (touch_information->TouchRadius);
            endRD.x = beginPointer.x + Touchsize.x * (touch_information->TouchRadius);
            endRD.y = beginPointer.y + Touchsize.y * (touch_information->TouchRadius);
            maxMove.x = (Touchsize.x * touch_information->floatswitch[3] / touch_information->floatswitch[0]);
            maxMove.y = (Touchsize.y * touch_information->floatswitch[3] / touch_information->floatswitch[0]);
            ThreadSpeed = 2000 / touch_information->floatswitch[0];
        }
    }

    bool StartAim() {
        if (botio) {
            clock_gettime(CLOCK_MONOTONIC, &endtime);
            loadTime = ((1000000000 * endtime.tv_sec) + (endtime.tv_nsec)) - ((1000000000 * strtime.tv_sec) + (strtime.tv_nsec));
            if (loadTime >= 5000000) {
                nowPointer = beginPointer;
                return true;
            }
            return false;
        } else {
            nowPointer = beginPointer;
            botio = true;
            botstart = true;
            mspeed.x = 0.2;
            mspeed.y = 0.2;
            return true;
        }
    }

    float GetMinAngle(float Target, float self, float offset) {
        float d1, d2;
        Target += offset;
        d1 = Target - self;
        d2 = 360.0 - fabs(d1);
        if (d1 > 0) {
            d2 *= -1.0;
        }
        return fabs(d1) < fabs(d2) ? d1 : d2;
    }

    void setTouchProportion(Vec2 *TouchProportion_) {
        TouchProportion = TouchProportion_;
    }

    bool AimRotArithmetic(Vec2 TouchMouses, Vec2 TouchAimRot) {
        if (isnormal(TouchAimRot.x) && isnormal(TouchAimRot.y)) {
            if (resolution_information->Orientation == 1) {
                AimRot.x = GetMinAngle(TouchAimRot.x, TouchMouses.x, 0.05);
                AimRot.y = GetMinAngle(TouchAimRot.y, TouchMouses.y, 0.03);
            } else if (resolution_information->Orientation == 3) {
                AimRot.x = -GetMinAngle(TouchAimRot.x, TouchMouses.x, 0.05);
                AimRot.y = -GetMinAngle(TouchAimRot.y, TouchMouses.y, 0.03);
            }
            if (fabs(AimRot.x) > (touch_information->Accuracy_X / touch_information->Scal)) {
                mspeed.x += 0.1;
                if (mspeed.x > (touch_information->floatswitch[2] * ThreadSpeed) * touch_information->Scal) {
                    mspeed.x = (touch_information->floatswitch[2] * ThreadSpeed);
                }
            } else {
                mspeed.x -= 0.1;
                if (mspeed.x < 0.2) {
                    mspeed.x = 0.2;
                }
            }
            if (fabs(AimRot.y) > (touch_information->Accuracy_Y / touch_information->Scal)) {
                mspeed.y += 0.1;
                if (mspeed.y > (touch_information->floatswitch[1] * ThreadSpeed) * touch_information->Scal) {
                    mspeed.y = (touch_information->floatswitch[1] * ThreadSpeed);
                }
            } else {
                mspeed.y -= 0.1;
                if (mspeed.y < 0.2) {
                    mspeed.y = 0.2;
                }
            }
            if (fabs(AimRot.x) > maxMove.x) {
                AimMove.x = AimRot.x > 0 ? maxMove.x : -(maxMove.x);
                nowPointer.x += AimMove.x;
            } else {
                AimMove.x = (AimRot.x * mspeed.x) * TouchProportion->x;
                nowPointer.x += AimMove.x;
            }
            if (fabs(AimRot.y) > maxMove.y) {
                AimMove.y = AimRot.y > 0 ? maxMove.y : -(maxMove.y);
                nowPointer.y += AimMove.y;
            } else {
                AimMove.y = (AimRot.y * mspeed.y) * TouchProportion->y;
                nowPointer.y += AimMove.y;
            }
            if (nowPointer.x < endLU.x || nowPointer.y < endLU.y || nowPointer.x > endRD.x || nowPointer.y > endRD.y) {
                clock_gettime(CLOCK_MONOTONIC, &strtime);
                return false;
            }
            return true;
        } else {
            if (nowPointer.x < endLU.x || nowPointer.y < endLU.y || nowPointer.x > endRD.x || nowPointer.y > endRD.y) {
                clock_gettime(CLOCK_MONOTONIC, &strtime);
                return false;
            }
            return true;
        }
    }

    void botEnd() {
        botio = false;
    }
};

class touch {
    private:
    bool IsInit = false;
	bool Aimio = false;
	bool IsFire = false;
    int FullScreenX = 0;
	int FullScreenY = 0;
	bool AimAtIo = false;
    BotTouch *botTouch = nullptr;
	TOUCH_INFORMATION* touch_information = nullptr;
	RESOLUTION_INFORMATION* resolution_information = nullptr;

	Vec2 RotatePoint(float PointX, float PointY, Vec2 Display) {
        Vec2 Rotate = {0, 0};
        if (resolution_information->Orientation == 3) {
            Rotate.x = Display.y - PointY;
            Rotate.y = PointX;
        } else if (resolution_information->Orientation == 2) {
            Rotate.x = Display.x - PointX;
            Rotate.y = Display.y - PointY;
        } else if (resolution_information->Orientation == 1) {
            Rotate.x = PointY;
            Rotate.y = Display.x - PointX;
        } else if (resolution_information->Orientation == 0) {
    		Rotate.x = PointX;
        	Rotate.y = PointY;
		}
    	return Rotate;
    }

    public:
    Vec2 TouchProportion;

	Vec2 FastAtan2(Vec3 Enem, Vec3 Self) {
		Vec2 AimCoordinates;
		float RotationX = Enem.x - Self.x;
		float RotationY = Enem.y - Self.y;
		float RotationZ = Enem.z - Self.z;
		float RotationH = sqrt((RotationX * RotationX) + (RotationY * RotationY));
		AimCoordinates.x = atan2(RotationZ, RotationH) * 180 / M_PI;
		if (RotationX >= 1 && RotationY >= 1) {
			AimCoordinates.y = atan(RotationY / RotationX) * 180 / M_PI;
		} else if (RotationX >= 1 && RotationY <= 1) {
			AimCoordinates.y = atan(RotationY / RotationX) * 180 / M_PI;
		} else if (RotationX <= 1 && RotationY >= 1) {
			AimCoordinates.y = atan(RotationY / RotationX) * 180 / M_PI + 180;
		} else if (RotationX <= 1 && RotationY <= 1) {
			AimCoordinates.y = atan(RotationY / RotationX) * 180 / M_PI - 180;
		}
		return AimCoordinates;
	}

    void setAimIo(bool Aimio_) {
        Aimio = Aimio_;
    }

    void GetTouchProportion(float touchmax_X, float touchmax_Y) {
        FullScreenX = resolution_information->FixedScreenWidth;
        FullScreenY = resolution_information->FixedScreenHeiht;
        TouchProportion.x  = touchmax_X / FullScreenY;
        TouchProportion.y  = touchmax_Y / FullScreenX;
    }

    void setFireIo(bool io){
        IsFire = io;
    }

    void GetTouch(TOUCH_INFORMATION* _touch_information, RESOLUTION_INFORMATION* _resolution_information, Vec2 _touch_size) {
	    if (!IsInit) {
	        touch_information = _touch_information;
	        resolution_information = _resolution_information;

	        GetTouchProportion(_touch_size.x, _touch_size.y);
	        botTouch = new BotTouch(_touch_information, _resolution_information, _touch_size);
	        botTouch->setTouchProportion(&TouchProportion);
	        IsInit = true;
	    }

        if (Aimio) {
            if (touch_information->TouchOrientationControl) {
                touch_information->TouchOrientationControl = false;
                GetTouchProportion(_touch_size.x, _touch_size.y);
                botTouch->setTouch();
            }
            if (!AimAtIo) {
                if (botTouch->StartAim()) {
                    Touch_Down(8, botTouch->beginPointer.x, botTouch->beginPointer.y);
                    AimAtIo = true;
                }
            } else {
                if (botTouch->AimRotArithmetic(touch_information->MouseCoordinate, touch_information->AimingCoordinates)) {
                    Touch_Move(8, botTouch->nowPointer.x, botTouch->nowPointer.y);
                } else {
                    Touch_Up(8);
                    AimAtIo = false;
                }
            }
        } else {
            if (AimAtIo) {
                Touch_Up(8);
                botTouch->botEnd();
                AimAtIo = false;
            }
        }
    }
};

extern touch touchdriven;

#endif // XCJ_TOUCH_H