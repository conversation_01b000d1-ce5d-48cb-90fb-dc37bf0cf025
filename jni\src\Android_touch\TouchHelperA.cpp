#include <dirent.h>
#include <fcntl.h>
#include <unistd.h>
#include <cmath>
#include <linux/input.h>
#include <linux/uinput.h>
#include <thread>
#include <random>
#include <imgui.h>
#include <cstdio>
#include <cstdlib>
#include <cstring>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <sys/time.h>
#include <sys/types.h>
#include <linux/types.h>

#include "timer.h"
#include "imgui_internal.h"
#include "TouchHelperA.h"
#include "ConfigManager.h"
#include "draw.h"
#include "driver.h"

using namespace std;
using std::string;

// 内核触摸驱动对象
extern Driver touchDriver;

struct eventinfo {
	int ID;
	bool io = false;
	struct input_absinfo data;
};

//触控设备数据结构
struct events {
	int ID;
	bool io = false;
	bool infoio = false;
	eventinfo eventmsg[KEY_MAX + 1];
};

float x_proportion, y_proportion;
int ScreenType = 0; // 竖屏 横屏
TouchFinger Fingers[10];
TouchFinger Aim_Fingers[10];
events eventdata[EV_MAX + 1];

int thread_us = 20;
bool grabImGuiEvent = false;

int GetEventCount()
{
	DIR *dir = opendir("/dev/input/");
	dirent *ptr = NULL;
	int count = 0;
	while ((ptr = readdir(dir)) != NULL)
	{
		if (strstr(ptr->d_name, "event"))
			count++;
	}
	return count;
}

bool checkDeviceIsTouch(int fd) {
	uint8_t *bits = NULL;
	ssize_t bits_size = 0;
	int res, j, k;
	bool itmp = false, itmp2 = false, itmp3 = false;
	struct input_absinfo abs{};
	while (true) {
		res = ioctl(fd, EVIOCGBIT(EV_ABS, bits_size), bits);
		if (res < bits_size)
			break;
		bits_size = res + 16;
		bits = (uint8_t *) realloc(bits, bits_size * 2);
	}
	for (j = 0; j < res; j++) {
		for (k = 0; k < 8; k++)
			if (bits[j] & 1 << k && ioctl(fd, EVIOCGABS(j * 8 + k), &abs) == 0) {
				if (j * 8 + k == ABS_MT_SLOT) {
					itmp = true;
					continue;
				}
				if (j * 8 + k == ABS_MT_POSITION_X) {
					itmp2 = true;
					continue;
				}
				if (j * 8 + k == ABS_MT_POSITION_Y) {
					itmp3 = true;
					continue;
				}
			}
	}
	free(bits);
	return itmp && itmp2 && itmp3;
}

int GetEventDriverfd() {
	DIR *dir = opendir("/dev/input/");
	if (!dir) {
		return false;
	}

	dirent *ptr = nullptr;
	int eventCount = 0;
	while ((ptr = readdir(dir)) != nullptr) {
		if (strstr(ptr->d_name, "event"))
			eventCount++;
	}

	char temp[128];
	for (int i = 0; i <= eventCount; i++) {
		sprintf(temp, "/dev/input/event%d", i);
		int fd = open(temp, O_RDWR);
		if (fd < 0) {
			continue;
		}
		if (checkDeviceIsTouch(fd)) {
			close(fd);
			fd = open(temp, O_RDONLY | O_SYNC | O_NONBLOCK);
			return fd;
		}
		close(fd);
	}

	return -1;
}

int fb,dev_fd;
int last_slot = -1;
bool touch_status = false;
bool TouchLock = false;
int global_tracking_id = 0;
bool windowHasFoused = false;


void Upload() {
    if (dev_fd <= 0) {
		return; // 都没抢夺触摸屏上传你妈逼
	}
	int32_t last_tmpCnt = 0;
	int32_t tmpCnt = 0;
	struct input_event event[128];

	for (int i = 0; i < 10; i ++) {
        auto& Finger = Fingers[i];
        if (Fingers[i].status == FINGER_NO) {
            continue;
    	}

        if (Finger.status != FINGER_UP) {
        	if (!touch_status)
    		{
    			touch_status = true;
    			event[tmpCnt].type = EV_KEY;
    			event[tmpCnt].code = BTN_TOUCH;
    			event[tmpCnt].value = 1;
    			tmpCnt++;
    		}

            if (last_slot != i)
    		{
    			event[tmpCnt].type = EV_ABS;
    		    event[tmpCnt].code = ABS_MT_SLOT;
    	        event[tmpCnt].value = i;
    		    tmpCnt++;
    			last_slot = i;
    		}

    	    event[tmpCnt].type = EV_ABS;
    		event[tmpCnt].code = ABS_MT_TRACKING_ID;
    	    event[tmpCnt].value = Finger.tracking_id;
    		tmpCnt++;

    		bool x_update = Finger.status == FINGER_X_UPDATE || Finger.status == FINGER_XY_UPDATE;
    		bool y_update = Finger.status == FINGER_Y_UPDATE || Finger.status == FINGER_XY_UPDATE;

    		if (x_update) {
    		    event[tmpCnt].type = EV_ABS;
    		    event[tmpCnt].code = ABS_MT_POSITION_X;
    	        event[tmpCnt].value = Finger.x;
    		    tmpCnt++;
    	    }
    		if (y_update) {
    			event[tmpCnt].type = EV_ABS;
    		    event[tmpCnt].code = ABS_MT_POSITION_Y;
    	        event[tmpCnt].value = Finger.y;
    		    tmpCnt++;
    		}

        	event[tmpCnt].type = EV_SYN;
            event[tmpCnt].code = SYN_REPORT;
            event[tmpCnt].value = 0;
            tmpCnt++;
        } else {
            if (last_slot != i)
    		{
    			event[tmpCnt].type = EV_ABS;
    		    event[tmpCnt].code = ABS_MT_SLOT;
    	        event[tmpCnt].value = i;
    		    tmpCnt++;
    			last_slot = i;
    		}

    		event[tmpCnt].type = EV_ABS;
    	    event[tmpCnt].code = ABS_MT_TRACKING_ID;
            event[tmpCnt].value = -1;
    	    tmpCnt++;

    		event[tmpCnt].type = EV_SYN;
            event[tmpCnt].code = SYN_REPORT;
            event[tmpCnt].value = 0;
            tmpCnt++;

    		touch_status = false;
        }

        for (int j = last_tmpCnt; j < tmpCnt; j ++) {
            event[j].time = Finger.time;
        }

        Finger.status = FINGER_NO;
		last_tmpCnt = tmpCnt;
	}

	if (last_tmpCnt) {
		write(dev_fd, event, sizeof(struct input_event) * tmpCnt);
	}
}

void slot_Upload(int slot) {
    if (dev_fd <= 0) {
		return;
	}

    auto& Finger = Fingers[slot];
    if (Fingers[slot].status == FINGER_NO) {
        return;
	}

	input_event event[16];
    // 开始上传数据

    int32_t tmpCnt = 0;
    // 重置事件

    if (Finger.status != FINGER_UP) {
    	if (!touch_status)
		{
			touch_status = true;
			event[tmpCnt].type = EV_KEY;
			event[tmpCnt].code = BTN_TOUCH;
			event[tmpCnt].value = 1;
			tmpCnt++;
		}

        if (last_slot != slot)
		{
			event[tmpCnt].type = EV_ABS;
		    event[tmpCnt].code = ABS_MT_SLOT;
	        event[tmpCnt].value = slot;
		    tmpCnt++;
			last_slot = slot;
		}

	    event[tmpCnt].type = EV_ABS;
		event[tmpCnt].code = ABS_MT_TRACKING_ID;
	    event[tmpCnt].value = Finger.tracking_id;
		tmpCnt++;

		bool x_update = Finger.status == FINGER_X_UPDATE || Finger.status == FINGER_XY_UPDATE;
		bool y_update = Finger.status == FINGER_Y_UPDATE || Finger.status == FINGER_XY_UPDATE;

		if (x_update) {
		    event[tmpCnt].type = EV_ABS;
		    event[tmpCnt].code = ABS_MT_POSITION_X;
	        event[tmpCnt].value = Finger.x;
		    tmpCnt++;
	    }
		if (y_update) {
			event[tmpCnt].type = EV_ABS;
		    event[tmpCnt].code = ABS_MT_POSITION_Y;
	        event[tmpCnt].value = Finger.y;
		    tmpCnt++;
		}

    	event[tmpCnt].type = EV_SYN;
        event[tmpCnt].code = SYN_REPORT;
        event[tmpCnt].value = 0;
        tmpCnt++;
    } else {
        if (last_slot != slot)
		{
			event[tmpCnt].type = EV_ABS;
		    event[tmpCnt].code = ABS_MT_SLOT;
	        event[tmpCnt].value = slot;
		    tmpCnt++;
			last_slot = slot;
		}

		event[tmpCnt].type = EV_ABS;
	    event[tmpCnt].code = ABS_MT_TRACKING_ID;
        event[tmpCnt].value = -1;
	    tmpCnt++;

		event[tmpCnt].type = EV_SYN;
        event[tmpCnt].code = SYN_REPORT;
        event[tmpCnt].value = 0;
        tmpCnt++;

		touch_status = false;
    }

    for (int i = 0; i < tmpCnt; i ++) {
        event[i].time = Finger.time;
    }

    Finger.status = FINGER_NO;

	write(dev_fd, event, sizeof(struct input_event) * tmpCnt);
}

void Touch_Down(int slot, int x, int y) {
    // 使用内核层触摸驱动
    touchDriver.uinput_down(x, y);
}

void Touch_Move(int slot, int x,int y){
    // 使用内核层触摸驱动
    touchDriver.uinput_move(x, y);
}

void Touch_Up(int slot) {
    // 使用内核层触摸驱动
    touchDriver.uinput_up();
}

void HandleTouchEvent() {
	input_absinfo absX{}, absY{};
	fb = GetEventDriverfd();

	ioctl(fb, EVIOCGRAB, 1);
	ioctl(fb, EVIOCGABS(ABS_MT_POSITION_X), &absX);
	ioctl(fb, EVIOCGABS(ABS_MT_POSITION_Y), &absY);

	float Width = absX.maximum;
	float Height = absY.maximum;

	int scr_x = displayInfo.width;
	int scr_y = displayInfo.height;

	if (scr_x > scr_y) {
		swap(scr_x, scr_y);
	}

    x_proportion = Width / scr_x;
    y_proportion = Height / scr_y;

	int slot = 0;
	int code, value;
	input_event in_ev;
	auto *io = &ImGui::GetIO();

	Timer MainAimiSleep;
	MainAimiSleep.SetFps(touch_information.floatswitch[0]);
	MainAimiSleep.AotuFPS_init();
	GrabTouchScreen();
	while (true) {
		if (touch_information.TouchOrientationControl)
			MainAimiSleep.SetFps(touch_information.floatswitch[0]);
		touchdriven.GetTouch(&touch_information, &resolution_information, Vec2(Width, Height));

		while (read(fb, &in_ev, sizeof(in_ev)) > 0) {
			if (in_ev.code != SYN_REPORT) {
				code = in_ev.code;
				value = in_ev.value;

				auto& Finger = Fingers[slot];
				Finger.time = in_ev.time;

				if (code == ABS_MT_POSITION_Y) {
					if (slot == 0) {
						if (displayInfo.orientation == 0)
							io->MousePos.y = value / y_proportion;
						else if(displayInfo.orientation == 1)
							io->MousePos.x = value / y_proportion;
						else
							io->MousePos.x = scr_y - value / y_proportion;
					}
					Finger.y = value;
					Finger.status |= FINGER_Y_UPDATE;
				} else if(code == ABS_MT_POSITION_X) {
					if (slot == 0) {
						if (displayInfo.orientation == 0)
							io->MousePos.x = value / x_proportion;
						else if(displayInfo.orientation == 1)
							io->MousePos.y = scr_x - value / x_proportion;
						else
							io->MousePos.y = value / x_proportion;
					}
					Finger.x = value;
					Finger.status |= FINGER_X_UPDATE;
				} else if (code == ABS_MT_TRACKING_ID) {
					if (value == -1){
						if(slot == 0)
							io->MouseDown[0] = false;

						Finger.status = FINGER_UP;
					} else {
						if(slot == 0)
							io->MouseDown[0] = true;

						Finger.tracking_id = global_tracking_id;
						global_tracking_id ++;
					}
				} else if(code == ABS_MT_SLOT) {
					slot = value;
				}
			} else {
				Upload();
				break;
			}
		}
		MainAimiSleep.AotuFPS();
	}
}

bool GrabTouchScreen() {
	static int uinp_fd;
	uinput_user_dev uinp{};

	uinp_fd = open("/dev/uinput", O_WRONLY | O_NONBLOCK);

	if (uinp_fd == 0) { // 打开uinput
		printf("无法打开 /dev/uinput 请检查是否有root权限\n");
		return false;
	}

	dev_fd = uinp_fd;

	// configure touch device event properties
	memset(&uinp, 0, sizeof(uinp));

	input_absinfo absX;
	input_absinfo absY;
    ioctl(fb, EVIOCGABS(ABS_MT_POSITION_X), &absX);
    ioctl(fb, EVIOCGABS(ABS_MT_POSITION_Y), &absY);

	uinp.id.bustype = 0;
	uinp.id.vendor = rand() % 10 + 3;
	uinp.id.product = rand() % 10 + 4;
	uinp.id.version = rand() % 10 + 5;

	char name[16];
	strncpy(uinp.name, "READTOUCHAIMING", UINPUT_MAX_NAME_SIZE);
	ioctl(uinp_fd, UI_SET_PHYS, name);

	int fd = fb;
	if (fd) {
		struct input_id id;
		if (!ioctl(fd, EVIOCGID, &id)) {
			uinp.id.bustype = id.bustype;
			uinp.id.vendor = id.vendor;
			uinp.id.product = id.product;
			uinp.id.version = id.version;
		}
		uint8_t *bits = NULL;
		ssize_t bits_size = 0;
		int res, j, k;
		while (1) {
			res = ioctl(fd, EVIOCGBIT(EV_KEY, bits_size), bits);
			if (res < bits_size)
				break;
			bits_size = res + 16;
			bits = (uint8_t *) realloc(bits, bits_size * 2);
		}
		for (j = 0; j < res; j++) {
			for (k = 0; k < 8; k++) {
				if (bits[j] & 1 << k) {
					if (j * 8 + k == BTN_TOUCH || j * 8 + k == BTN_TOOL_FINGER)
						continue;
					ioctl(uinp_fd, UI_SET_KEYBIT, j * 8 + k);
				}
			}
		}
		free(bits);
	}

    uinp.absmin[ABS_MT_SLOT] = 0;
    uinp.absmax[ABS_MT_SLOT] = 9;
    uinp.absmin[ABS_MT_TRACKING_ID] = 0;
    uinp.absmax[ABS_MT_TRACKING_ID] = 65535;// 按键码ID累计叠加最大值

    uinp.absmin[ABS_MT_POSITION_X] = 0;
    uinp.absmax[ABS_MT_POSITION_X] = absX.maximum;

    uinp.absmin[ABS_MT_POSITION_Y] = 0;
    uinp.absmax[ABS_MT_POSITION_Y] = absY.maximum;

    ioctl(uinp_fd, UI_SET_PROPBIT, INPUT_PROP_DIRECT);

    ioctl(uinp_fd, UI_SET_EVBIT, EV_ABS);
    ioctl(uinp_fd, UI_SET_ABSBIT, ABS_MT_SLOT);
    ioctl(uinp_fd, UI_SET_ABSBIT, ABS_MT_POSITION_X);
    ioctl(uinp_fd, UI_SET_ABSBIT, ABS_MT_POSITION_Y);
    ioctl(uinp_fd, UI_SET_ABSBIT, ABS_MT_TRACKING_ID);

    ioctl(uinp_fd, UI_SET_EVBIT, EV_SYN);

	/* Create input device into input sub-system */
	write(uinp_fd, &uinp, sizeof(uinp));
	if (ioctl(uinp_fd, UI_DEV_CREATE)) {
		return false;
	}

	printf("创建触摸屏成功! \n");
	return true;
}

// 统一触摸事件处理函数（处理ImGui事件 + 重新注入系统事件）
void HandleImGuiTouchEvent() {
	input_absinfo absX{}, absY{};
	int fb = GetEventDriverfd();
	if (fb < 0) {
		printf("无法获取触摸设备\n");
		return;
	}

	ioctl(fb, EVIOCGRAB, 1);
	ioctl(fb, EVIOCGABS(ABS_MT_POSITION_X), &absX);
	ioctl(fb, EVIOCGABS(ABS_MT_POSITION_Y), &absY);

	float Width = absX.maximum;
	float Height = absY.maximum;

	int scr_x = displayInfo.width;
	int scr_y = displayInfo.height;

	if (scr_x > scr_y) {
		swap(scr_x, scr_y);
	}

    float x_proportion = Width / scr_x;
    float y_proportion = Height / scr_y;

	int slot = 0;
	int code, value;
	input_event in_ev;
	auto *io = &ImGui::GetIO();

	Timer MainAimiSleep;
	MainAimiSleep.SetFps(touch_information.floatswitch[0]);
	MainAimiSleep.AotuFPS_init();
	GrabTouchScreen();

	while (true) {
		if (touch_information.TouchOrientationControl)
			MainAimiSleep.SetFps(touch_information.floatswitch[0]);
		touchdriven.GetTouch(&touch_information, &resolution_information, Vec2(Width, Height));

		while (read(fb, &in_ev, sizeof(in_ev)) > 0) {
			if (in_ev.code != SYN_REPORT) {
				code = in_ev.code;
				value = in_ev.value;

				auto& Finger = Fingers[slot];
				Finger.time = in_ev.time;

				if (code == ABS_MT_POSITION_Y) {
					if (slot == 0) {
						if (displayInfo.orientation == 0)
							io->MousePos.y = value / y_proportion;
						else if(displayInfo.orientation == 1)
							io->MousePos.x = value / y_proportion;
						else
							io->MousePos.x = scr_y - value / y_proportion;
					}
					Finger.y = value;
					Finger.status |= FINGER_Y_UPDATE;
				} else if(code == ABS_MT_POSITION_X) {
					if (slot == 0) {
						if (displayInfo.orientation == 0)
							io->MousePos.x = value / x_proportion;
						else if(displayInfo.orientation == 1)
							io->MousePos.y = scr_x - value / x_proportion;
						else
							io->MousePos.y = value / x_proportion;
					}
					Finger.x = value;
					Finger.status |= FINGER_X_UPDATE;
				} else if (code == ABS_MT_TRACKING_ID) {
					if (value == -1){
						if(slot == 0)
							io->MouseDown[0] = false;

						Finger.status = FINGER_UP;
					} else {
						if(slot == 0)
							io->MouseDown[0] = true;

						Finger.tracking_id = global_tracking_id;
						global_tracking_id ++;
					}
				} else if(code == ABS_MT_SLOT) {
					slot = value;
				}
			} else {
				Upload();
				break;
			}
		}
		MainAimiSleep.AotuFPS();
	}
}