./obj/local/arm64-v8a/objs/test.sh/src/main.o: jni/src/main.cpp \
  jni/include/My_Hack/AimBot.h jni/include/My_Tools/timer.h \
  jni/include/Android_draw/AndroidImgui.h \
  jni/include/ImGui/imgui_Custom.h jni/include/ImGui/imgui.h \
  jni/include/ImGui/imconfig.h jni/include/ImGui/imgui_internal.h \
  jni/include/ImGui/imstb_textedit.h \
  jni/include/My_Tools/ConfigManager.h \
  jni/include/My_Tools/VectorTools.h jni/include/Android_draw/json.hpp \
  jni/include/Android_Graphics/GraphicsManager.h \
  jni/include/My_Tools/HttpClient.h jni/include/My_Tools/KernelTools.h \
  jni/include/Android_draw/draw.h \
  jni/include/native_surface/ANativeWindowCreator.h \
  jni/include/Android_touch/TouchHelperA.h \
  jni/include/Android_my_imgui/my_imgui.h jni/include/My_Tools/driver.h \
  jni/include/ImGui/dynamic_Island.h
jni/include/My_Hack/AimBot.h:
jni/include/My_Tools/timer.h:
jni/include/Android_draw/AndroidImgui.h:
jni/include/ImGui/imgui_Custom.h:
jni/include/ImGui/imgui.h:
jni/include/ImGui/imconfig.h:
jni/include/ImGui/imgui_internal.h:
jni/include/ImGui/imstb_textedit.h:
jni/include/My_Tools/ConfigManager.h:
jni/include/My_Tools/VectorTools.h:
jni/include/Android_draw/json.hpp:
jni/include/Android_Graphics/GraphicsManager.h:
jni/include/My_Tools/HttpClient.h:
jni/include/My_Tools/KernelTools.h:
jni/include/Android_draw/draw.h:
jni/include/native_surface/ANativeWindowCreator.h:
jni/include/Android_touch/TouchHelperA.h:
jni/include/Android_my_imgui/my_imgui.h:
jni/include/My_Tools/driver.h:
jni/include/ImGui/dynamic_Island.h:
