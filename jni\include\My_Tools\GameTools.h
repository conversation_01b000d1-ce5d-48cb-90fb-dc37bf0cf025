//
// Created by s2049 on 24-7-9.
//

#ifndef GAMETOOLS_H
#define GAMETOOLS_H

#include <cstdint>
#include <unordered_map>

#include "VectorTools.h"

typedef char UTF8;
typedef unsigned short UTF16;

struct Actors {
  uint64_t Enc_1, Enc_2;
  uint64_t Enc_3, Enc_4;
};

struct Chunk {
  uint32_t val_1, val_2, val_3, val_4;
  uint32_t val_5, val_6, val_7, val_8;
};

struct MaterialsInto {
  string name;
  unordered_map<string, string> Map;
  unordered_map<string, bool> MapBOOL;
  ImColor color;
};

// 载具数据结构
struct VehicleInfo {
  uintptr_t address; // 载具地址
  Vec3 Pos;          // 载具位置
  Vec2 ScreenPos;    // 屏幕位置
  float Distance;    // 与玩家距离
  string Name;       // 载具名称
  string ClassName;  // 类名
};

enum GameSetAPI {
  ChinaPUBG,
  PUBG
};

namespace GameOffset {
// 世界投类地址
extern uintptr_t UWorld_Offset;
extern uintptr_t UWorldLast_Offset; // PUBG专属
extern uintptr_t GNames_Offset;
extern uintptr_t GNamesLast_Offset; // PUBG专属
extern uintptr_t ULeve_Offset;
extern uintptr_t GMatrix_Offset;
extern uintptr_t GMatrix_Last_Offset; // PUBG专属
extern uintptr_t LineOfSightTo_Offset;

// 控制器类地址
extern uintptr_t Selfaddress_Offset;
extern uintptr_t UWorldToNectDriver_Offset;
extern uintptr_t NetDriverToServerConnection_Offset;
extern uintptr_t ServerConnectionToPlayerController_Offset;
extern uintptr_t PlayerControllerToMyselfActor_Offset;
extern uintptr_t ULeveToActors_Offset;
extern uintptr_t ULeveToActorsCount_Offset;
extern uintptr_t STExtraPlayerController_Offset;

// 对象属性类地址
extern uintptr_t PlayerTeam_Offset;
extern uintptr_t PlayerCameraManager_Offset;
extern uintptr_t WeaponManagerComponent_Offset;
extern uintptr_t CurrentWeaponReplicated_Offset;
extern uintptr_t CurrentUsingWeaponSafety_Offset;
extern uintptr_t WeaponEntityComp_Offset;
extern uintptr_t ShootWeaponType_Offset;
extern uintptr_t CameraCache_Offset;
extern uintptr_t CameraPOV_Offset;
extern uintptr_t CameraLocation_Offset;
extern uintptr_t CameraLocationLocalSpace_Offset;
extern uintptr_t CameraRotation_Offset;
extern uintptr_t CameraFOV_Offset;
extern uintptr_t ControlRotation_Offset;
extern uintptr_t RootComponent_Offset;
extern uintptr_t PlayertWorldPos_Offset;
extern uintptr_t IsFiring_Offset;
extern uintptr_t PlayerHight_Offset;
extern uintptr_t State_Offset;
extern uintptr_t Health_Offset;
extern uintptr_t bDead_Offset;
extern uintptr_t IsBot_Offset;
extern uintptr_t PlayerName_Offset;
extern uintptr_t BoneMesh_Offset;
extern uintptr_t BoneHuman_Offset;
extern uintptr_t BonePtr_Offset;

extern int NowGameSet;
bool InitToGameOffset(int GameSet);
} // namespace GameOffset

namespace GameTools {
extern short bone_getcount;
extern bool IsStartRead;
extern bool IsGetCodePtr;
extern Vec2 HalfscreenSize;
extern unordered_map<int, string> ClassCache;
extern unordered_map<string, string> GrenadeClassNameMap;
extern unordered_map<string, string> VehicleClassNameMap;                   // 添加载具映射表
extern unordered_map<int, unordered_map<string, string>> ItemsClassNameMap; // 添加物资分类映射表
extern vector<smokeObject> smokeObjects;
extern vector<MaterialsInto> MaterialsIntoVector;
extern vector<VehicleInfo> MainVehicleList; // 添加载具列表
extern uintptr_t Gname_address, Bone_address;
extern uintptr_t infdata, CodePtr;
extern FTransform meshtrans, headtrans;
extern FMatrix c2wMatrix, boneMatrix;
extern FMatrix worldMatrix;

uint64_t DecryptActorsArray(uint64_t uLevel, int Actors_Offset, int EncryptedActors_Offset);
/**
 * @brief 根据索引值从 FName 对象中获取字符串
 * @param index 字符串索引值
 * @return 返回获取的字符串，索引无效则返回空字符串
 */
string GetFromFName(int index);

// 获取玩家名字的函数
/**
 * @brief 获取玩家名字的函数
 * @param namepy 指向 PUBG 玩家名字的指针
 * @return 返回玩家名字的字符指针
 */
char *getPlayerName(long namepy);

// 检查是否能看到给定的三维点
/**
 * @brief 检查到目标点的视线是否受阻
 * @param Poin 一个三维向量，代表目标点的位置
 * @return 返回 true 表示视线不受阻，否则返回 false
 */
bool LineOfSightTo(BoneStruct Poin);

// 初始化用于获取角色骨骼信息的函数
/**
 * @brief 初始化获取角色骨骼信息的函数
 * @param Human 指向角色对象的指针
 * @param _Bone_address 骨头信息的地址
 * @return 返回 true 表示初始化成功，否则返回 false
 */
bool GetBoneInit(uintptr_t Human, uintptr_t _Bone_address);

// 根据骨骼编号获取骨骼数据的函数
/**
 * @brief 根据骨骼编号获取骨骼数据的函数
 * @param BoneNumber 骨骼编号
 * @param tBone 指向骨头结构的指针
 * @return 返回 true 表示获取成功，否则返回 false
 */
bool GetBoneData(int BoneNumber, BoneStruct *tBone);

// 根据骨骼编号和位置编号获取骨骼数据的函数
/**
 * @brief 根据骨骼编号和位置编号获取骨骼数据的函数
 * @param BoneNumber 骨骼编号
 * @param tBone 指向骨头结构的指针
 * @param PosZ 位置编号
 * @return 返回 true 表示获取成功，否则返回 false
 */
bool GetBoneData(int BoneNumber, BoneStruct *tBone, int PosZ);

/**
 * 计算两个向量之间的距离。
 *
 * @param self 物体的位置向量。
 * @param other 视角的位置向量。
 * @return 距离。
 */
float GetDistance(const Vec2 &self, const Vec2 &other);

/**
 * 计算两个向量之间的距离。
 *
 * @param Object 物体的位置向量。
 * @param Self 视角的位置向量。
 * @param Distance 存储计算得到的距离的变量指针。
 * @return 无。
 */
void GetDistance(Vec3 Object, Vec3 Self, float *Distance);

/**
 * 将人物欧拉角转换为矩阵
 *
 * @param rotation 人物欧拉角。
 * @return 矩阵。
 */
FMatrix rotatorToMatrix(Rotator rotation);

/**
 * 将世界坐标系中的物体坐标转换为屏幕坐标。重载函数。
 *
 * @param bscreen 存储转换得到的屏幕坐标的变量指针。
 * @param obj 物体的世界坐标。
 * @return 无。
 */
void WorldToScreen(Vec2 *bscreen, Vec3 *obj);

/**
 * 将世界坐标系中的物体坐标转换为屏幕坐标。重载函数。
 *
 * @param bscreen 存储转换得到的屏幕坐标的变量指针。
 * @param obj 物体的世界坐标。
 * @return 无。
 */
void WorldToScreen(Vec2 *bscreen, Vec3 obj);

/**
 * 将世界坐标系中的物体坐标转换为屏幕坐标。
 *
 * @param obj 物体的世界坐标。
 * @return 返回转换得到的屏幕坐标。
 */
Vec2 WorldToScreen(Vec3 obj);

/**
 * 将矩阵表示的向量转换为三维向量。
 *
 * @param matrix 矩阵。
 * @return 返回转换得到的三维向量。
 */
Vec3 MarixToVector(FMatrix matrix);

/**
 * 计算两个矩阵的乘积。
 *
 * @param m1 第一个矩阵。
 * @param m2 第二个矩阵。
 * @return 返回计算得到的矩阵乘积。
 */
FMatrix MatrixMulti(FMatrix m1, FMatrix m2);

/**
 * 将转换矩阵转换为矩阵表示的变换。
 *
 * @param transform 转换矩阵。
 * @return 返回转换得到的变换矩阵。
 */
FMatrix TransformToMatrix(FTransform transform);

/**
 * 根据给定的内存地址获取骨骼转换信息。
 *
 * @param addr 骨骼数据的内存地址。
 * @return 返回骨骼的转换信息。
 */
FTransform getBone(uintptr_t addr);

/**
 * 将本地坐标系转换为目标坐标系的旋转信息。
 *
 * @param local 本地坐标系的位置。
 * @param target 目标坐标系的位置。
 * @return 返回转换后的旋转信息。
 */
Rotator toRotator(Vec3 local, Vec3 target);

/**
 * 计算子弹撞击点的位置
 *
 * @param angle 子弹发射的角度，用于计算子弹的飞行轨迹
 * @param distance 子弹飞行的距离，决定了子弹飞行的远近
 * @return 返回子弹撞击点的位置，以Vec3类型表示，包含了x、y、z三个坐标轴的信息
 */
Vec3 calculateBulletImpact(const Rotator &angle, float distance);

/**
 * 获取目标角度与当前角度之间的最小差值。
 *
 * @param Target 目标角度。
 * @param self 当前角度。
 * @return 返回最小差值。
 */
float getMinAngle(float Target, float self);

/**
 * 将坐标绕原点旋转指定角度。
 *
 * @param angle 旋转角度。
 * @param objRadar_x 原始坐标的 x 分量。
 * @param objRadar_y 原始坐标的 y 分量。
 * @return 返回旋转后的坐标。
 */
Vec2 rotateCoord(float angle, float objRadar_x, float objRadar_y);

/**
 * 计算给定圆心坐标、圆的半径和旋转角度后圆上点的新坐标
 *
 * @param center 圆心坐标。
 * @param radius 圆的半径
 * @param angleDegree 旋转角度
 * @return 返回旋转后的坐标。
 */
Vec2 rotatePoint(Vec2 center, float radius, float angleDegree);

} // namespace GameTools

#endif // GAMETOOLS_H